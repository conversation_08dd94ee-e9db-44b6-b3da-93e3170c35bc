package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.projectile.thrown.EnderPearlEntity;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.hit.HitResult;
import net.minecraft.world.RaycastContext;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class PearlFollow extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    
    private final Setting<Double> maxDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("max-distance")
        .description("Maximum distance to follow pearls.")
        .defaultValue(50.0)
        .range(10.0, 100.0)
        .sliderRange(10.0, 100.0)
        .build());
    
    private final Setting<Double> throwDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("throw-delay")
        .description("Delay in seconds before throwing follow pearl.")
        .defaultValue(0.1)
        .range(0.0, 2.0)
        .sliderRange(0.0, 2.0)
        .build());
    
    private final Setting<Boolean> onlyEnemies = sgGeneral.add(new BoolSetting.Builder()
        .name("only-enemies")
        .description("Only follow pearls thrown by enemies (not teammates).")
        .defaultValue(true)
        .build());
    
    private final Setting<Boolean> predictLanding = sgGeneral.add(new BoolSetting.Builder()
        .name("predict-landing")
        .description("Predict where the pearl will land and throw to that location.")
        .defaultValue(true)
        .build());
    
    private final Setting<Boolean> detailedLogging = sgGeneral.add(new BoolSetting.Builder()
        .name("detailed-logging")
        .description("Enable detailed logging for debugging.")
        .defaultValue(false)
        .build());

    private final Setting<Boolean> debugMode = sgGeneral.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Debug mode: throws pearl straight ahead when you have a pearl in hand.")
        .defaultValue(false)
        .build());

    private final Setting<Boolean> debugTrigger = sgGeneral.add(new BoolSetting.Builder()
        .name("debug-trigger")
        .description("Trigger debug pearl throw (automatically resets).")
        .defaultValue(false)
        .visible(() -> debugMode.get())
        .build());

    private final Setting<Boolean> useMovementBoosts = sgGeneral.add(new BoolSetting.Builder()
        .name("use-movement-boosts")
        .description("Use jumping, sprinting, and momentum for optimal pearl throws.")
        .defaultValue(true)
        .build());

    private final Setting<Double> preparationTime = sgGeneral.add(new DoubleSetting.Builder()
        .name("preparation-time")
        .description("Maximum time in seconds to prepare optimal throw (jumping, positioning).")
        .defaultValue(0.5)
        .range(0.1, 2.0)
        .sliderRange(0.1, 2.0)
        .visible(() -> useMovementBoosts.get())
        .build());

    // Track pearls and their predicted landing spots
    private final Map<UUID, PearlTracker> trackedPearls = new ConcurrentHashMap<>();
    private final Set<UUID> processedPearls = new HashSet<>();
    private long lastThrowTime = 0;

    // Enhanced throwing mechanics
    private ThrowPreparation currentPreparation = null;
    private boolean wasSprintingBefore = false;
    private boolean wasJumpingBefore = false;

    public PearlFollow() {
        super(AddonTemplate.CATEGORY, "pearl-follow", "Automatically throws pearls to follow enemy pearl throws.");
    }

    @Override
    public void onActivate() {
        if (debugMode.get()) {
            info("Pearl Follow activated in DEBUG MODE!");
            info("Toggle 'debug-trigger' to throw a pearl straight ahead anytime.");
            info("Enemy pearl tracking is also active in debug mode.");
        } else {
            info("Pearl Follow activated - tracking enemy pearls...");
        }
    }

    private static class PearlTracker {
        final EnderPearlEntity pearl;
        final Vec3d initialPos;
        final Vec3d initialVel;
        final long throwTime;
        Vec3d predictedLanding;
        boolean landingCalculated = false;

        PearlTracker(EnderPearlEntity pearl) {
            this.pearl = pearl;
            this.initialPos = pearl.getPos();
            this.initialVel = pearl.getVelocity();
            this.throwTime = System.currentTimeMillis();
        }
    }

    private static class ThrowPreparation {
        final Vec3d targetPos;
        final long startTime;
        final OptimalThrowPlan plan;
        boolean isExecuting = false;
        int currentStep = 0;

        ThrowPreparation(Vec3d targetPos, OptimalThrowPlan plan) {
            this.targetPos = targetPos;
            this.plan = plan;
            this.startTime = System.currentTimeMillis();
        }
    }

    private static class OptimalThrowPlan {
        final boolean shouldJump;
        final boolean shouldSprint;
        final Vec3d optimalPosition;
        final Vec3d throwDirection;
        final double totalTime;
        final double pearlSpeed;
        final String strategy;

        OptimalThrowPlan(boolean shouldJump, boolean shouldSprint, Vec3d optimalPosition,
                        Vec3d throwDirection, double totalTime, double pearlSpeed, String strategy) {
            this.shouldJump = shouldJump;
            this.shouldSprint = shouldSprint;
            this.optimalPosition = optimalPosition;
            this.throwDirection = throwDirection;
            this.totalTime = totalTime;
            this.pearlSpeed = pearlSpeed;
            this.strategy = strategy;
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }

        // Check for debug trigger
        if (debugMode.get() && debugTrigger.get()) {
            executeDebugThrow();
            debugTrigger.set(false); // Reset trigger
            return;
        }

        // Handle ongoing throw preparation
        if (currentPreparation != null) {
            handleThrowPreparation();
            return; // Don't process new pearls while preparing a throw
        }

        // Clean up old/invalid pearls
        cleanupPearls();

        // Find new pearls to track
        findNewPearls();

        // Process tracked pearls
        processTrackedPearls();
    }

    private void cleanupPearls() {
        trackedPearls.entrySet().removeIf(entry -> {
            PearlTracker tracker = entry.getValue();
            // Remove if pearl is removed from world or too old (10 seconds)
            boolean shouldRemove = !tracker.pearl.isAlive() || 
                                 System.currentTimeMillis() - tracker.throwTime > 10000;
            
            if (shouldRemove && detailedLogging.get()) {
                info("Cleaned up pearl tracker for pearl " + entry.getKey());
            }
            
            return shouldRemove;
        });
        
        // Clean up processed pearls set (keep only recent ones)
        processedPearls.removeIf(uuid -> {
            return trackedPearls.values().stream()
                .noneMatch(tracker -> tracker.pearl.getUuid().equals(uuid));
        });
    }

    private void findNewPearls() {
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EnderPearlEntity pearl)) continue;
            
            UUID pearlId = pearl.getUuid();
            
            // Skip if already tracking or processed
            if (trackedPearls.containsKey(pearlId) || processedPearls.contains(pearlId)) continue;
            
            // Skip if too far away
            double distance = mc.player.getPos().distanceTo(pearl.getPos());
            if (distance > maxDistance.get()) continue;
            
            // Skip if thrown by player (our own pearl)
            if (pearl.getOwner() == mc.player) continue;
            
            // Skip if thrown by teammate (if onlyEnemies is enabled)
            if (onlyEnemies.get() && pearl.getOwner() != null && isTeammate(pearl.getOwner())) {
                continue;
            }
            
            // Start tracking this pearl
            PearlTracker tracker = new PearlTracker(pearl);
            trackedPearls.put(pearlId, tracker);

            String throwerName = pearl.getOwner() != null ? pearl.getOwner().getName().getString() : "Unknown";
            if (debugMode.get()) {
                info("DEBUG MODE: Detected pearl from " + throwerName + " - will throw straight ahead when ready");
            } else if (detailedLogging.get()) {
                info("Started tracking pearl thrown by " + throwerName + " at " + pearl.getPos());
            }
        }
    }

    private void processTrackedPearls() {
        for (Map.Entry<UUID, PearlTracker> entry : trackedPearls.entrySet()) {
            PearlTracker tracker = entry.getValue();
            
            // Skip if already processed
            if (processedPearls.contains(entry.getKey())) continue;
            
            // Calculate landing prediction if enabled and not done yet
            if (predictLanding.get() && !tracker.landingCalculated) {
                tracker.predictedLanding = calculateLandingSpot(tracker);
                tracker.landingCalculated = true;
                
                if (detailedLogging.get() && tracker.predictedLanding != null) {
                    info("Predicted landing spot: " + tracker.predictedLanding);
                }
            }
            
            // Check if we should throw our pearl
            if (shouldThrowFollowPearl(tracker)) {
                if (debugMode.get()) {
                    // Debug mode: throw straight ahead
                    throwDebugPearl(tracker);
                } else if (useMovementBoosts.get()) {
                    // Enhanced throwing with movement optimization
                    startEnhancedThrow(tracker);
                } else {
                    // Basic throwing
                    throwFollowPearl(tracker);
                }
                processedPearls.add(entry.getKey());
            }
        }
    }

    private Vec3d calculateLandingSpot(PearlTracker tracker) {
        Vec3d pos = tracker.initialPos;
        Vec3d vel = tracker.initialVel;
        
        // Physics constants (approximate Minecraft values)
        double gravity = 0.03;
        double drag = 0.99;
        
        // Simulate pearl trajectory
        for (int tick = 0; tick < 300; tick++) { // Max 15 seconds
            // Update position
            pos = pos.add(vel);
            
            // Apply drag and gravity
            vel = vel.multiply(drag);
            vel = vel.add(0, -gravity, 0);
            
            // Check for collision with blocks
            HitResult hitResult = mc.world.raycast(new RaycastContext(
                pos.subtract(vel), pos, 
                RaycastContext.ShapeType.COLLIDER, 
                RaycastContext.FluidHandling.NONE, 
                tracker.pearl
            ));
            
            if (hitResult.getType() != HitResult.Type.MISS) {
                return hitResult.getPos();
            }
            
            // Check if pearl is below world
            if (pos.y < mc.world.getBottomY()) {
                break;
            }
        }
        
        return pos; // Fallback to last calculated position
    }

    private boolean shouldThrowFollowPearl(PearlTracker tracker) {
        // Check if we have pearls
        FindItemResult pearlResult = InvUtils.find(Items.ENDER_PEARL);
        if (!pearlResult.found()) {
            if (detailedLogging.get()) {
                info("No ender pearls found in inventory");
            }
            return false;
        }
        
        // Check throw delay
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastThrowTime < throwDelay.get() * 1000) {
            return false;
        }
        
        // Check if pearl is still in reasonable range
        double distance = mc.player.getPos().distanceTo(tracker.pearl.getPos());
        if (distance > maxDistance.get()) {
            return false;
        }
        
        return true;
    }

    private void throwFollowPearl(PearlTracker tracker) {
        FindItemResult pearlResult = InvUtils.find(Items.ENDER_PEARL);
        if (!pearlResult.found()) return;
        
        // Determine target position
        Vec3d targetPos;
        if (predictLanding.get() && tracker.predictedLanding != null) {
            targetPos = tracker.predictedLanding;
        } else {
            targetPos = tracker.pearl.getPos();
        }
        
        // Calculate optimal throw angle and velocity
        Vec3d throwVector = calculateOptimalThrow(mc.player.getEyePos(), targetPos);
        if (throwVector == null) {
            if (detailedLogging.get()) {
                error("Could not calculate optimal throw vector");
            }
            return;
        }
        
        // Swap to pearl if needed
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        int originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
        if (mc.player.getMainHandStack().getItem() != Items.ENDER_PEARL) {
            InvUtils.swap(pearlResult.slot(), false);
        }
        
        // Set aim direction
        float[] angles = vectorToAngles(throwVector);
        mc.player.setYaw(angles[0]);
        mc.player.setPitch(angles[1]);
        
        // Throw pearl
        markActionTaken(); // Notify action coordinator
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);

        // Swap back if needed
        if (originalSlot != pearlResult.slot()) {
            InvUtils.swap(originalSlot, false);
        }

        lastThrowTime = System.currentTimeMillis();
        
        String throwerName = tracker.pearl.getOwner() != null ? 
            tracker.pearl.getOwner().getName().getString() : "Unknown";
        info("Threw follow pearl targeting " + throwerName + "'s pearl");
        
        if (detailedLogging.get()) {
            info("Target position: " + targetPos);
            info("Throw vector: " + throwVector);
        }
    }

    private Vec3d calculateOptimalThrow(Vec3d from, Vec3d to) {
        Vec3d diff = to.subtract(from);
        double horizontalDist = Math.sqrt(diff.x * diff.x + diff.z * diff.z);
        double verticalDist = diff.y;
        
        // Pearl physics constants
        double gravity = 0.03;
        double initialSpeed = 1.5; // Approximate pearl throw speed
        
        // Calculate optimal angle using projectile motion equations
        double discriminant = Math.pow(initialSpeed, 4) - gravity * (gravity * horizontalDist * horizontalDist + 2 * verticalDist * initialSpeed * initialSpeed);
        
        if (discriminant < 0) {
            // Target unreachable with current speed, use 45-degree angle
            double angle = Math.toRadians(45);
            double yaw = Math.atan2(diff.z, diff.x);
            
            return new Vec3d(
                Math.cos(yaw) * Math.cos(angle) * initialSpeed,
                Math.sin(angle) * initialSpeed,
                Math.sin(yaw) * Math.cos(angle) * initialSpeed
            );
        }
        
        // Use the lower angle for efficiency
        double angle = Math.atan((initialSpeed * initialSpeed - Math.sqrt(discriminant)) / (gravity * horizontalDist));
        double yaw = Math.atan2(diff.z, diff.x);
        
        return new Vec3d(
            Math.cos(yaw) * Math.cos(angle) * initialSpeed,
            Math.sin(angle) * initialSpeed,
            Math.sin(yaw) * Math.cos(angle) * initialSpeed
        );
    }

    private float[] vectorToAngles(Vec3d vector) {
        double yaw = Math.toDegrees(Math.atan2(vector.z, vector.x)) - 90.0;
        double pitch = -Math.toDegrees(Math.atan2(vector.y, Math.sqrt(vector.x * vector.x + vector.z * vector.z)));
        
        return new float[]{(float) yaw, (float) pitch};
    }

    private boolean isTeammate(Entity entity) {
        // Simple teammate check - can be enhanced based on your needs
        if (mc.player.getScoreboardTeam() != null && entity.getScoreboardTeam() != null) {
            return mc.player.getScoreboardTeam().equals(entity.getScoreboardTeam());
        }
        return false;
    }

    private void startEnhancedThrow(PearlTracker tracker) {
        // Determine target position
        Vec3d targetPos;
        if (predictLanding.get() && tracker.predictedLanding != null) {
            targetPos = tracker.predictedLanding;
        } else {
            targetPos = tracker.pearl.getPos();
        }

        // Calculate optimal throw plan
        OptimalThrowPlan plan = calculateOptimalThrowPlan(mc.player.getPos(), targetPos);

        if (plan == null) {
            // Fall back to basic throw if planning fails
            throwFollowPearl(tracker);
            return;
        }

        // Start preparation
        currentPreparation = new ThrowPreparation(targetPos, plan);
        wasSprintingBefore = mc.player.isSprinting();

        if (detailedLogging.get()) {
            info("Starting enhanced throw preparation: " + plan.strategy);
            info("Plan: Jump=" + plan.shouldJump + ", Sprint=" + plan.shouldSprint +
                 ", Time=" + String.format("%.2f", plan.totalTime) + "s");
        }
    }

    private void handleThrowPreparation() {
        if (currentPreparation == null) return;

        long elapsed = System.currentTimeMillis() - currentPreparation.startTime;
        double elapsedSeconds = elapsed / 1000.0;

        // Check if preparation time exceeded
        if (elapsedSeconds > preparationTime.get()) {
            executeEnhancedThrow();
            return;
        }

        OptimalThrowPlan plan = currentPreparation.plan;

        // Execute preparation steps
        if (plan.shouldSprint && !mc.player.isSprinting()) {
            mc.player.setSprinting(true);
        }

        if (plan.shouldJump && mc.player.isOnGround()) {
            // Jump at optimal timing
            double timeToThrow = plan.totalTime - elapsedSeconds;
            if (timeToThrow <= 0.3) { // Jump 0.3 seconds before throw
                mc.player.jump();
                if (detailedLogging.get()) {
                    info("Jumping for enhanced throw");
                }
            }
        }

        // Check if we should execute the throw now
        if (elapsedSeconds >= plan.totalTime * 0.8) { // Execute at 80% of planned time
            executeEnhancedThrow();
        }
    }

    private OptimalThrowPlan calculateOptimalThrowPlan(Vec3d from, Vec3d to) {
        Vec3d diff = to.subtract(from);
        double horizontalDist = Math.sqrt(diff.x * diff.x + diff.z * diff.z);
        double verticalDist = diff.y;

        // Base pearl physics
        double baseSpeed = 1.5;
        double gravity = 0.03;

        // Calculate different throwing strategies
        OptimalThrowPlan[] strategies = new OptimalThrowPlan[4];

        // Strategy 1: Basic throw (no movement boosts)
        strategies[0] = calculateThrowStrategy(from, to, baseSpeed, false, false, "Basic");

        // Strategy 2: Sprint throw (10% speed boost)
        double sprintSpeed = baseSpeed * 1.1;
        strategies[1] = calculateThrowStrategy(from, to, sprintSpeed, false, true, "Sprint");

        // Strategy 3: Jump throw (higher initial position, slight speed boost)
        Vec3d jumpFrom = from.add(0, 1.2, 0); // Jump height
        double jumpSpeed = baseSpeed * 1.05;
        strategies[2] = calculateThrowStrategy(jumpFrom, to, jumpSpeed, true, false, "Jump");

        // Strategy 4: Sprint + Jump throw (combined boosts)
        double combinedSpeed = baseSpeed * 1.15;
        strategies[3] = calculateThrowStrategy(jumpFrom, to, combinedSpeed, true, true, "Sprint+Jump");

        // Find the strategy with the shortest total time
        OptimalThrowPlan bestPlan = null;
        double shortestTime = Double.MAX_VALUE;

        for (OptimalThrowPlan plan : strategies) {
            if (plan != null && plan.totalTime < shortestTime) {
                shortestTime = plan.totalTime;
                bestPlan = plan;
            }
        }

        return bestPlan;
    }

    private OptimalThrowPlan calculateThrowStrategy(Vec3d from, Vec3d to, double speed,
                                                   boolean shouldJump, boolean shouldSprint, String strategy) {
        Vec3d diff = to.subtract(from);
        double horizontalDist = Math.sqrt(diff.x * diff.x + diff.z * diff.z);
        double verticalDist = diff.y;

        if (horizontalDist < 0.1) {
            return null; // Too close
        }

        double gravity = 0.03;

        // Calculate optimal angle using projectile motion
        double discriminant = Math.pow(speed, 4) - gravity * (gravity * horizontalDist * horizontalDist + 2 * verticalDist * speed * speed);

        if (discriminant < 0) {
            // Target unreachable, use 45-degree angle
            double angle = Math.toRadians(45);
            double yaw = Math.atan2(diff.z, diff.x);

            Vec3d throwDirection = new Vec3d(
                Math.cos(yaw) * Math.cos(angle) * speed,
                Math.sin(angle) * speed,
                Math.sin(yaw) * Math.cos(angle) * speed
            );

            // Estimate time (rough approximation)
            double estimatedTime = (2 * speed * Math.sin(angle)) / gravity;

            return new OptimalThrowPlan(shouldJump, shouldSprint, from, throwDirection,
                                      estimatedTime, speed, strategy + " (45°)");
        }

        // Use the lower angle for efficiency (faster arrival)
        double angle = Math.atan((speed * speed - Math.sqrt(discriminant)) / (gravity * horizontalDist));
        double yaw = Math.atan2(diff.z, diff.x);

        Vec3d throwDirection = new Vec3d(
            Math.cos(yaw) * Math.cos(angle) * speed,
            Math.sin(angle) * speed,
            Math.sin(yaw) * Math.cos(angle) * speed
        );

        // Calculate precise flight time
        double flightTime = horizontalDist / (speed * Math.cos(angle));

        // Add preparation time if movement boosts are used
        double preparationTime = 0;
        if (shouldJump) preparationTime += 0.3; // Time to jump
        if (shouldSprint) preparationTime += 0.1; // Time to start sprinting

        double totalTime = preparationTime + flightTime;

        return new OptimalThrowPlan(shouldJump, shouldSprint, from, throwDirection,
                                  totalTime, speed, strategy);
    }

    private void executeEnhancedThrow() {
        if (currentPreparation == null) return;

        FindItemResult pearlResult = InvUtils.find(Items.ENDER_PEARL);
        if (!pearlResult.found()) {
            currentPreparation = null;
            return;
        }

        OptimalThrowPlan plan = currentPreparation.plan;

        // Swap to pearl if needed
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        int originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
        if (mc.player.getMainHandStack().getItem() != Items.ENDER_PEARL) {
            InvUtils.swap(pearlResult.slot(), false);
        }

        // Set optimal aim direction
        float[] angles = vectorToAngles(plan.throwDirection);
        mc.player.setYaw(angles[0]);
        mc.player.setPitch(angles[1]);

        // Throw pearl with enhanced velocity
        markActionTaken(); // Notify action coordinator
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);

        // Swap back if needed
        if (originalSlot != -1 && originalSlot != pearlResult.slot()) {
            InvUtils.swap(originalSlot, false);
        }

        // Restore original sprint state
        mc.player.setSprinting(wasSprintingBefore);

        lastThrowTime = System.currentTimeMillis();

        info("Enhanced pearl thrown using strategy: " + plan.strategy);
        if (detailedLogging.get()) {
            info("Final velocity boost: " + String.format("%.2f", plan.pearlSpeed) + " m/s");
            info("Target position: " + currentPreparation.targetPos);
        }

        currentPreparation = null;
    }

    private void executeDebugThrow() {
        FindItemResult pearlResult = InvUtils.find(Items.ENDER_PEARL);
        if (!pearlResult.found()) {
            error("Debug mode: No ender pearls found in inventory!");
            return;
        }

        // Swap to pearl if needed
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        int originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
        if (mc.player.getMainHandStack().getItem() != Items.ENDER_PEARL) {
            InvUtils.swap(pearlResult.slot(), false);
        }

        // Don't change aim - throw straight ahead
        info("DEBUG MODE: Throwing pearl straight ahead!");

        // Throw pearl
        markActionTaken(); // Notify action coordinator
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);

        // Swap back if needed
        if (originalSlot != -1 && originalSlot != pearlResult.slot()) {
            InvUtils.swap(originalSlot, false);
        }

        lastThrowTime = System.currentTimeMillis();
        info("Debug pearl thrown straight ahead");
    }

    private void throwDebugPearl(PearlTracker tracker) {
        FindItemResult pearlResult = InvUtils.find(Items.ENDER_PEARL);
        if (!pearlResult.found()) return;

        // Swap to pearl if needed
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        int originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
        if (mc.player.getMainHandStack().getItem() != Items.ENDER_PEARL) {
            InvUtils.swap(pearlResult.slot(), false);
        }

        // Don't change aim - throw straight ahead
        info("Debug mode: Throwing pearl straight ahead");

        // Throw pearl
        markActionTaken(); // Notify action coordinator
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);

        // Swap back if needed
        if (originalSlot != -1 && originalSlot != pearlResult.slot()) {
            InvUtils.swap(originalSlot, false);
        }

        lastThrowTime = System.currentTimeMillis();

        String throwerName = tracker.pearl.getOwner() != null ?
            tracker.pearl.getOwner().getName().getString() : "Unknown";
        info("Debug pearl thrown (straight ahead) for " + throwerName + "'s pearl");
    }

    @Override
    public void onDeactivate() {
        trackedPearls.clear();
        processedPearls.clear();
        currentPreparation = null;

        // Restore original movement state
        if (mc.player != null) {
            mc.player.setSprinting(wasSprintingBefore);
        }
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    // Mark that this module is taking action this tick
    private void markActionTaken() {
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            actionTakenField.setBoolean(null, true);
        } catch (Exception e) {
            // Ignore if we can't set the flag
        }
    }
}
