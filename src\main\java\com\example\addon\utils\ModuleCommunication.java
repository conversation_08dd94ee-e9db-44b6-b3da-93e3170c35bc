package com.example.addon.utils;

import net.minecraft.entity.Entity;

/**
 * Utility class for communication between modules.
 * Provides a centralized way for modules to share state and coordinate actions.
 */
public class ModuleCommunication {
    
    // Sprint reset communication
    private static boolean sprintResetRequested = false;
    private static Entity sprintResetTarget = null;
    private static long sprintResetRequestTime = 0;
    private static final long SPRINT_RESET_TIMEOUT = 100; // 100ms timeout
    
    /**
     * Request a sprint reset from the W-Tap module.
     * This should be called by AutoClicker when it performs an attack.
     * 
     * @param target The entity being attacked (for context)
     */
    public static void requestSprintReset(Entity target) {
        sprintResetRequested = true;
        sprintResetTarget = target;
        sprintResetRequestTime = System.currentTimeMillis();
    }
    
    /**
     * Check if a sprint reset has been requested.
     * This should be called by W-Tap module to check for pending requests.
     * 
     * @return true if a sprint reset is requested and not timed out
     */
    public static boolean isSprintResetRequested() {
        // Check if request has timed out
        if (sprintResetRequested && (System.currentTimeMillis() - sprintResetRequestTime) > SPRINT_RESET_TIMEOUT) {
            clearSprintResetRequest();
            return false;
        }
        return sprintResetRequested;
    }
    
    /**
     * Get the target entity for the sprint reset request.
     * 
     * @return The entity that was attacked, or null if no request is pending
     */
    public static Entity getSprintResetTarget() {
        return sprintResetTarget;
    }
    
    /**
     * Clear the sprint reset request.
     * This should be called by W-Tap module after handling the request.
     */
    public static void clearSprintResetRequest() {
        sprintResetRequested = false;
        sprintResetTarget = null;
        sprintResetRequestTime = 0;
    }
    
    /**
     * Check if the sprint reset request is for a specific target.
     * 
     * @param target The target to check against
     * @return true if the request is for the specified target
     */
    public static boolean isSprintResetForTarget(Entity target) {
        return sprintResetRequested && sprintResetTarget != null && sprintResetTarget.equals(target);
    }
}
