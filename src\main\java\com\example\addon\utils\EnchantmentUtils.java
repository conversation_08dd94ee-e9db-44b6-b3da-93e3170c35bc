package com.example.addon.utils;

import net.minecraft.enchantment.Enchantment;
import net.minecraft.item.ItemStack;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.entry.RegistryEntry;

public class EnchantmentUtils {

    public static boolean hasEnchantment(ItemStack stack, RegistryKey<Enchantment> enchantmentKey) {
        if (stack.getEnchantments() == null) return false;
        for (RegistryEntry<Enchantment> entry : stack.getEnchantments().getEnchantments()) {
            if (entry.matchesKey(enchantmentKey)) {
                return true;
            }
        }
        return false;
    }
}