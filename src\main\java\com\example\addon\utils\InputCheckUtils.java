package com.example.addon.utils;

import net.minecraft.client.MinecraftClient;
import org.lwjgl.glfw.GLFW;

public class InputCheckUtils {
    public static boolean isLMBHeld() {
        MinecraftClient mc = MinecraftClient.getInstance();
        
        if (mc == null || mc.getWindow() == null) {
            return false;
        }
        
        long windowHandle = mc.getWindow().getHandle();
        int state = GLFW.glfwGetMouseButton(windowHandle, GLFW.GLFW_MOUSE_BUTTON_LEFT);
        
        return state == GLFW.GLFW_PRESS;
    }
    
    /**
     * Checks if the W key is currently pressed.
     * Used by combat modules to track movement input state.
     * 
     * @return true if the W key is pressed, false otherwise
     */
    public static boolean isWKeyPressed() {
        MinecraftClient mc = MinecraftClient.getInstance();
        
        if (mc == null || mc.getWindow() == null) {
            return false;
        }
        
        long windowHandle = mc.getWindow().getHandle();
        int state = GLFW.glfwGetKey(windowHandle, GLFW.GLFW_KEY_W);
        
        return state == GLFW.GLFW_PRESS;
    }
    
    /**
     * Checks if the S key is currently pressed.
     * Used by combat modules to track movement input state.
     * 
     * @return true if the S key is pressed, false otherwise
     */
    public static boolean isSKeyPressed() {
        MinecraftClient mc = MinecraftClient.getInstance();
        
        if (mc == null || mc.getWindow() == null) {
            return false;
        }
        
        long windowHandle = mc.getWindow().getHandle();
        int state = GLFW.glfwGetKey(windowHandle, GLFW.GLFW_KEY_S);
        
        return state == GLFW.GLFW_PRESS;
    }
}