package com.example.addon.modules;

import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.block.BlockState;

import java.util.HashSet;
import java.util.Set;

public class AutoLava extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    // General settings
    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("Maximum range to place lava.")
        .defaultValue(4.0)
        .min(0.0)
        .max(6.0)
        .sliderMax(6.0)
        .build()
    );

    private final Setting<Boolean> selfProtect = sgGeneral.add(new BoolSetting.Builder()
        .name("self-protect")
        .description("Avoid placing lava on yourself.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between lava placements in ticks.")
        .defaultValue(5)
        .min(0)
        .max(20)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> autoPickup = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-pickup")
        .description("Automatically pick up lava when enemy takes out water, empty bucket, or blocks.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> debugMode = sgGeneral.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Print debug information about placement and pickup decisions.")
        .defaultValue(false)
        .build()
    );

    // State tracking
    private int timer = 0;
    private PlayerEntity target = null;
    private int originalSlot = -1;
    private final Set<PlayerEntity> lavaTargets = new HashSet<>();
    private final Set<BlockPos> placedLavaBlocks = new HashSet<>();

    public AutoLava() {
        super(Categories.Combat, "auto-lava", "Automatically places lava on enemies and picks it up when they counter.");
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Decrement timer
        if (timer > 0) {
            timer--;
            return;
        }

        // Check for lava pickup first
        if (autoPickup.get()) {
            checkForLavaPickup();
        }

        // Find and target enemy
        target = findTarget();
        if (target == null) return;

        // Check if we have lava bucket
        FindItemResult lavaBucket = InvUtils.find(Items.LAVA_BUCKET);
        if (!lavaBucket.found()) {
            if (debugMode.get()) {
                System.out.println("[AutoLava] No lava bucket found in inventory");
            }
            return;
        }

        // Find placement position
        BlockPos placePos = getPlacementPos(target);
        if (placePos != null && canPlaceAt(placePos) && !willAffectPlayer(placePos)) {
            placeLava(placePos, lavaBucket);
        }
    }

    private void checkForLavaPickup() {
        if (placedLavaBlocks.isEmpty()) return;

        // Check if any targeted players are holding counter items
        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player || !lavaTargets.contains(player)) continue;

            ItemStack mainHand = player.getMainHandStack();
            ItemStack offHand = player.getOffHandStack();

            // Check if player is holding water bucket, empty bucket, or blocks
            if (isCounterItem(mainHand) || isCounterItem(offHand)) {
                if (debugMode.get()) {
                    System.out.println("[AutoLava] Player " + player.getName().getString() + 
                                     " has counter item, picking up lava");
                }
                pickupNearbyLava(player);
                break;
            }
        }
    }

    private boolean isCounterItem(ItemStack stack) {
        if (stack.isEmpty()) return false;
        
        return stack.getItem() == Items.WATER_BUCKET ||
               stack.getItem() == Items.BUCKET ||
               stack.getItem().asItem().toString().contains("block") ||
               stack.getItem() == Items.COBBLESTONE ||
               stack.getItem() == Items.DIRT ||
               stack.getItem() == Items.SAND ||
               stack.getItem() == Items.GRAVEL;
    }

    private void pickupNearbyLava(PlayerEntity player) {
        FindItemResult emptyBucket = InvUtils.find(Items.BUCKET);
        if (!emptyBucket.found()) {
            if (debugMode.get()) {
                System.out.println("[AutoLava] No empty bucket found for pickup");
            }
            return;
        }

        // Find lava blocks near the player
        Vec3d playerPos = player.getPos();
        BlockPos playerBlockPos = new BlockPos((int) Math.floor(playerPos.x), 
                                             (int) Math.floor(playerPos.y), 
                                             (int) Math.floor(playerPos.z));

        for (BlockPos lavaPos : new HashSet<>(placedLavaBlocks)) {
            if (playerBlockPos.isWithinDistance(lavaPos, 3.0)) {
                // Check if it's still lava
                if (mc.world.getBlockState(lavaPos).getBlock() == Blocks.LAVA) {
                    // Store original slot and swap to bucket
                    if (originalSlot == -1) {
                        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                        originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
                    }

                    if (InvUtils.swap(emptyBucket.slot(), false)) {
                        // Try to pick up the lava
                        if (mc.interactionManager != null) {
                            mc.interactionManager.interactBlock(mc.player,
                                emptyBucket.getHand() != null ? emptyBucket.getHand() : mc.player.getActiveHand(),
                                new net.minecraft.util.hit.BlockHitResult(
                                    Vec3d.ofCenter(lavaPos),
                                    net.minecraft.util.math.Direction.UP,
                                    lavaPos,
                                    false
                                )
                            );
                        }
                        
                        placedLavaBlocks.remove(lavaPos);
                        swapBackToOriginal();
                        timer = delay.get();
                        
                        if (debugMode.get()) {
                            System.out.println("[AutoLava] Picked up lava at " + lavaPos);
                        }
                        return;
                    }
                }
            }
        }
    }

    private PlayerEntity findTarget() {
        PlayerEntity closestTarget = null;
        double closestDistance = Double.MAX_VALUE;

        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;

            double distance = mc.player.distanceTo(player);
            if (distance > range.get()) continue;

            // Check if player is already affected by lava
            if (isPlayerInLava(player)) continue;

            if (distance < closestDistance) {
                closestDistance = distance;
                closestTarget = player;
            }
        }

        return closestTarget;
    }

    private boolean isPlayerInLava(PlayerEntity player) {
        BlockPos playerPos = new BlockPos((int) Math.floor(player.getX()), 
                                        (int) Math.floor(player.getY()), 
                                        (int) Math.floor(player.getZ()));
        
        // Check current position and position above
        return mc.world.getBlockState(playerPos).getBlock() == Blocks.LAVA ||
               mc.world.getBlockState(playerPos.up()).getBlock() == Blocks.LAVA;
    }

    private BlockPos getPlacementPos(PlayerEntity target) {
        Vec3d targetPos = target.getPos();
        BlockPos targetFeet = new BlockPos((int) Math.floor(targetPos.x), 
                                         (int) Math.floor(targetPos.y), 
                                         (int) Math.floor(targetPos.z));

        // Try positions around the target
        BlockPos[] positions = {
            targetFeet,                    // Feet position
            targetFeet.add(1, 0, 0),      // East
            targetFeet.add(-1, 0, 0),     // West
            targetFeet.add(0, 0, 1),      // South
            targetFeet.add(0, 0, -1),     // North
            targetFeet.down()             // Below feet
        };

        for (BlockPos pos : positions) {
            if (canPlaceAt(pos)) {
                return pos;
            }
        }

        return null;
    }

    private boolean canPlaceAt(BlockPos pos) {
        // Check if position is within range
        if (!mc.player.getBlockPos().isWithinDistance(pos, range.get())) {
            return false;
        }
        
        // Check if block is air and can be replaced
        BlockState state = mc.world.getBlockState(pos);
        return state.isAir();
    }

    private boolean willAffectPlayer(BlockPos pos) {
        if (!selfProtect.get()) return false;
        
        Vec3d playerPos = mc.player.getPos();
        double distance = playerPos.distanceTo(Vec3d.ofCenter(pos));
        
        // Don't place lava too close to player
        return distance < 2.0;
    }

    private void placeLava(BlockPos pos, FindItemResult lavaBucket) {
        // Store original slot
        if (originalSlot == -1) {
            FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            originalSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
        }

        // Swap to lava bucket
        if (lavaBucket.getHand() == null && !InvUtils.swap(lavaBucket.slot(), false)) {
            swapBackToOriginal();
            return;
        }

        // Place the lava
        if (BlockUtils.place(pos, lavaBucket, false, 0)) {
            placedLavaBlocks.add(pos);
            lavaTargets.add(target);
            timer = delay.get();
            
            if (debugMode.get()) {
                System.out.println("[AutoLava] Placed lava at " + pos + " targeting " + target.getName().getString());
            }
        }

        swapBackToOriginal();
    }

    private void swapBackToOriginal() {
        if (originalSlot != -1) {
            FindItemResult currentSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            int currentSlot = currentSlotResult.found() ? currentSlotResult.slot() : -1;
            
            if (currentSlot != originalSlot) {
                InvUtils.swap(originalSlot, false);
            }
        }
        originalSlot = -1;
    }

    @Override
    public void onDeactivate() {
        timer = 0;
        target = null;
        lavaTargets.clear();
        placedLavaBlocks.clear();
        swapBackToOriginal();
    }
}
