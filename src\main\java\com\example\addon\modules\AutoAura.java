package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.Items;
import net.minecraft.util.math.Vec3d;

import java.util.function.Predicate;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class AutoAura extends Module {
    private enum AuraMode {
        MaceAura,
        StunSlam,
        AttributeSwap // Added AttributeSwap as a mode
    }

    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgSwitching = settings.createGroup("Auto Switching");

    // General settings
    private final Setting<Boolean> showMode = sgGeneral.add(new BoolSetting.Builder()
        .name("show-active-mode")
        .description("Display which mode is currently active in chat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> detailedLogging = sgGeneral.add(new BoolSetting.Builder()
        .name("detailed-logging")
        .description("Enable detailed logging for decision making.")
        .defaultValue(false)
        .build()
    );

    // Auto-switching settings
    private final Setting<Boolean> stunSlamForShields = sgSwitching.add(new BoolSetting.Builder()
        .name("stun-slam-for-shields")
        .description("Use StunSlam when target is blocking with a shield.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> maceAuraForNoShield = sgSwitching.add(new BoolSetting.Builder()
        .name("mace-aura-for-no-shield")
        .description("Use MaceAura when target is not blocking.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> attributeSwapOnGround = sgSwitching.add(new BoolSetting.Builder() // New setting
        .name("attribute-swap-on-ground")
        .description("Use AttributeSwap when on ground.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> minFallDistance = sgSwitching.add(new DoubleSetting.Builder() // New setting for fall distance
        .name("min-fall-distance")
        .description("Minimum fall distance to prevent AttributeSwap activation.")
        .defaultValue(3.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );

    private final Setting<Boolean> considerTargetHeight = sgSwitching.add(new BoolSetting.Builder()
        .name("consider-target-height")
        .description("Factor in target height when making decisions.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> maceAuraFlyingThreshold = sgSwitching.add(new DoubleSetting.Builder()
        .name("mace-aura-flying-threshold")
        .description("Use MaceAura when target is this high off the ground.")
        .defaultValue(3.0)
        .range(0.0, 10.0)
        .sliderRange(0.0, 10.0)
        .visible(considerTargetHeight::get)
        .build()
    );

    private final Setting<Boolean> considerMobility = sgSwitching.add(new BoolSetting.Builder()
        .name("consider-mobility")
        .description("Factor in target mobility items when making decisions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> modeChangeDelay = sgSwitching.add(new IntSetting.Builder()
        .name("mode-change-delay")
        .description("Delay (in ticks) before switching between modes.")
        .defaultValue(5)
        .range(0, 20)
        .sliderRange(0, 20)
        .build()
    );

    private final Setting<Boolean> waitForComboComplete = sgSwitching.add(new BoolSetting.Builder()
        .name("wait-for-combo-complete")
        .description("Don't switch away from StunSlam until its combo is finished.")
        .defaultValue(true)
        .build()
    );

    // Module references
    private MaceAura maceAura;
    private StunSlam stunSlam;
    private AttributeSwap attributeSwap; // Added AttributeSwap reference
    private Targets targetsModule;

    // State tracking
    private AuraMode currentMode = AuraMode.MaceAura;
    private AuraMode desiredMode = AuraMode.MaceAura;
    private int modeChangeTimer = 0;
    private LivingEntity currentTarget = null;
    private String lastDecisionReason = "";

    public AutoAura() {
        super(AddonTemplate.CATEGORY, "auto-aura", "Automatically switches between MaceAura and StunSlam based on combat scenarios.");
    }

    @Override
    public void onActivate() {
        // Initialize module references and verify their existence
        initializeModules();

        if (maceAura == null) {
            error("MaceAura module not found. AutoAura requires MaceAura to function.");
            toggle();
            return;
        }

        if (stunSlam == null) {
            error("StunSlam module not found. AutoAura requires StunSlam to function.");
            toggle();
            return;
        }

        if (attributeSwap == null) {
            error("AttributeSwap module not found. AutoAura requires AttributeSwap to function.");
            toggle();
            return;
        }

        if (targetsModule == null) {
            error("Targets module not found. AutoAura requires Targets to function.");
            toggle();
            return;
        }

        // Verify module versions are compatible by checking for required methods
        try {
            // Check if isSlamming method exists in StunSlam - required for AutoAura
            stunSlam.getClass().getMethod("isSlamming");
        } catch (NoSuchMethodException e) {
            error("StunSlam module is outdated or incompatible. It must have the isSlamming() method.");
            toggle();
            return;
        }

        // Ensure all modules are initially disabled
        if (maceAura.isActive()) maceAura.toggle();
        if (stunSlam.isActive()) stunSlam.toggle();
        if (attributeSwap.isActive()) attributeSwap.toggle();

        // Start with MaceAura by default
        currentMode = AuraMode.MaceAura;
        activateCurrentMode();
        
        if (showMode.get()) {
            info("AutoAura activated. Starting with: " + currentMode.name());
        }
    }

    @Override
    public void onDeactivate() {
        // Disable all modules when AutoAura is disabled
        if (maceAura != null && maceAura.isActive()) maceAura.toggle();
        if (stunSlam != null && stunSlam.isActive()) stunSlam.toggle();
        if (attributeSwap != null && attributeSwap.isActive()) attributeSwap.toggle();
        
        if (showMode.get()) {
            info("AutoAura deactivated.");
        }
    }

    private void initializeModules() {
        maceAura = Modules.get().get(MaceAura.class);
        stunSlam = Modules.get().get(StunSlam.class);
        attributeSwap = Modules.get().get(AttributeSwap.class); // Initialize AttributeSwap
        targetsModule = Modules.get().get(Targets.class);
    }

    private void log(String message) {
        if (detailedLogging.get()) {
            info(message);
        }
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            log("Another module has already taken action this tick. Skipping AutoAura.");
            return;
        }

        // Check if required modules are still available and re-initialize if needed
        if (maceAura == null || stunSlam == null || attributeSwap == null || targetsModule == null) {
            log("Re-initializing modules in onTick");
            initializeModules();
        }
        
        // If still null after initialization or Targets is not active, return
        if (maceAura == null || stunSlam == null || attributeSwap == null || targetsModule == null || !targetsModule.isActive()) {
            if (maceAura == null) log("MaceAura is null");
            if (stunSlam == null) log("StunSlam is null");
            if (attributeSwap == null) log("AttributeSwap is null");
            if (targetsModule == null) log("Targets is null");
            else if (!targetsModule.isActive()) log("Targets is not active");
            return;
        }

        // Handle mode change timer
        if (modeChangeTimer > 0) {
            modeChangeTimer--;
            return;
        }

        // Get current target using same logic as other modules
        Predicate<Entity> targetPredicate = entity -> {
            if (entity.equals(mc.player)) return false;
            
            Targets targetsModule = Modules.get().get(Targets.class);
            if (targetsModule == null || !targetsModule.isActive()) {
                return false;
            }
            
            // Use the new shouldTarget method to check if we should target this entity
            return targetsModule.shouldTarget(entity);
        };

        Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);
        
        if (foundTarget instanceof LivingEntity) {
            currentTarget = (LivingEntity) foundTarget;
        } else {
            currentTarget = null;
        }

        // If no target, just ensure current mode is active
        if (currentTarget == null) {
            if (!isCurrentModeActive()) {
                log("No target. Ensuring current mode is active");
                activateCurrentMode();
            }
            return;
        }

        // Determine optimal mode for current target
        desiredMode = determineOptimalMode(currentTarget);
        
        // Check if we need to switch modes
        if (desiredMode != currentMode) {
            log("Mode switch needed: " + currentMode.name() + " -> " + desiredMode.name());
            log(lastDecisionReason);
            
            currentMode = desiredMode;
            activateCurrentMode();
            modeChangeTimer = modeChangeDelay.get();
            
            if (showMode.get()) {
                info("Switched to: " + currentMode.name());
                if (detailedLogging.get()) {
                    info(lastDecisionReason);
                }
            }
        } else {
            // Ensure current mode is still active
            if (!isCurrentModeActive()) {
                log("Reactivating " + currentMode.name() + " (was disabled)");
                activateCurrentMode();
            }
        }
    }

    private void activateCurrentMode() {
        // Additional null safety checks
        if (maceAura == null || stunSlam == null || attributeSwap == null) {
            error("Cannot activate mode: Required modules are null");
            initializeModules();
            if (maceAura == null || stunSlam == null || attributeSwap == null) return;
        }
        
        // Disable all other modes first
        if (currentMode == AuraMode.MaceAura) {
            if (stunSlam.isActive()) {
                log("Disabling StunSlam before enabling MaceAura");
                stunSlam.toggle();
            }
            if (attributeSwap.isActive()) {
                log("Disabling AttributeSwap before enabling MaceAura");
                attributeSwap.toggle();
            }
            
            if (!maceAura.isActive()) {
                log("Enabling MaceAura");
                maceAura.toggle();
            }
        } else if (currentMode == AuraMode.StunSlam) {
            if (maceAura.isActive()) {
                log("Disabling MaceAura before enabling StunSlam");
                maceAura.toggle();
            }
            if (attributeSwap.isActive()) {
                log("Disabling AttributeSwap before enabling StunSlam");
                attributeSwap.toggle();
            }
            
            if (!stunSlam.isActive()) {
                log("Enabling StunSlam");
                stunSlam.toggle();
            }
        } else { // AttributeSwap
            if (maceAura.isActive()) {
                log("Disabling MaceAura before enabling AttributeSwap");
                maceAura.toggle();
            }
            if (stunSlam.isActive()) {
                log("Disabling StunSlam before enabling AttributeSwap");
                stunSlam.toggle();
            }
            
            if (!attributeSwap.isActive()) {
                log("Enabling AttributeSwap");
                attributeSwap.toggle();
            }
        }
    }

    private double getTargetHeightOffGround(LivingEntity target) {
        if (target == null || mc.world == null) return 0.0;
        
        Vec3d targetPos = target.getPos();
        double targetY = targetPos.y;
        
        // Simple ground detection - check for solid blocks below
        for (int y = (int)targetY; y >= mc.world.getBottomY(); y--) {
            if (!mc.world.getBlockState(new net.minecraft.util.math.BlockPos((int)targetPos.x, y, (int)targetPos.z))
                    .getCollisionShape(mc.world, new net.minecraft.util.math.BlockPos((int)targetPos.x, y, (int)targetPos.z)).isEmpty()) {
                return targetY - (y + 1);
            }
        }
        
        return targetY - mc.world.getBottomY();
    }

    private boolean hasFireworkOrWindCharge(LivingEntity target) {
        // Null safety check
        if (target == null) return false;
        
        return target.getMainHandStack().getItem() == Items.FIREWORK_ROCKET ||
               target.getOffHandStack().getItem() == Items.FIREWORK_ROCKET ||
               target.getMainHandStack().getItem() == Items.WIND_CHARGE ||
               target.getOffHandStack().getItem() == Items.WIND_CHARGE;
    }

    private AuraMode determineOptimalMode(LivingEntity target) {
        // Null safety checks
        if (target == null || mc.player == null) {
            return AuraMode.MaceAura; // Default fallback
        }

        // Re-initialize modules if any are null (handles edge cases)
        if (maceAura == null || stunSlam == null || attributeSwap == null) {
            log("Re-initializing modules in determineOptimalMode");
            initializeModules();
            
            // If still null after re-initialization, return default
            if (maceAura == null || stunSlam == null || attributeSwap == null) {
                return AuraMode.MaceAura;
            }
        }

        // CRITICAL: Don't switch away from StunSlam if it's actively performing a combo
        try {
            if (waitForComboComplete.get() && currentMode == AuraMode.StunSlam && stunSlam != null && stunSlam.isSlamming()) {
                lastDecisionReason = "StunSlam combo in progress - maintaining mode.";
                return AuraMode.StunSlam;
            }
        } catch (Exception e) {
            error("Error checking StunSlam state: " + e.getMessage());
            return currentMode; // Maintain current mode on error
        }

        // Check if player hasn't fallen the minimum distance and attribute swap is enabled
        if (attributeSwapOnGround.get() && mc.player.fallDistance < minFallDistance.get()) {
            lastDecisionReason = "Player hasn't fallen minimum distance - using AttributeSwap.";
            return AuraMode.AttributeSwap;
        }

        boolean targetBlocking = target.isBlocking();
        boolean targetHasMobility = considerMobility.get() && hasFireworkOrWindCharge(target);
        double targetHeight = considerTargetHeight.get() ? getTargetHeightOffGround(target) : 0.0;
        double distanceToTarget = mc.player.distanceTo(target);

        StringBuilder reasonBuilder = new StringBuilder("Mode decision: ");

        // Primary decision: Shield state
        if (targetBlocking && stunSlamForShields.get()) {
            reasonBuilder.append("target blocking with shield, ");
            lastDecisionReason = reasonBuilder.append("chose StunSlam for shield break combo.").toString();
            return AuraMode.StunSlam;
        }

        if (!targetBlocking && maceAuraForNoShield.get()) {
            reasonBuilder.append("target not blocking, ");
            
            // Secondary considerations when target isn't blocking
            if (considerTargetHeight.get() && targetHeight >= maceAuraFlyingThreshold.get()) {
                reasonBuilder.append("target flying (")
                    .append(String.format("%.1f", targetHeight))
                    .append(" blocks high), ");
                lastDecisionReason = reasonBuilder.append("chose MaceAura for air combat.").toString();
                return AuraMode.MaceAura;
            }
            
            if (targetHasMobility) {
                reasonBuilder.append("target has mobility items, ");
                lastDecisionReason = reasonBuilder.append("chose MaceAura for mobile target.").toString();
                return AuraMode.MaceAura;
            }
            
            // Default to MaceAura when target isn't blocking
            lastDecisionReason = reasonBuilder.append("chose MaceAura (default for non-blocking targets).").toString();
            return AuraMode.MaceAura;
        }

        // Fallback logic if both shield settings are disabled
        if (!stunSlamForShields.get() && !maceAuraForNoShield.get()) {
            if (targetBlocking) {
                lastDecisionReason = reasonBuilder.append("target blocking, defaulted to StunSlam.").toString();
                return AuraMode.StunSlam;
            } else {
                lastDecisionReason = reasonBuilder.append("target not blocking, defaulted to MaceAura.").toString();
                return AuraMode.MaceAura;
            }
        }
        
        // Final fallback
        lastDecisionReason = reasonBuilder.append("fallback to MaceAura.").toString();
        return AuraMode.MaceAura;
    }

    private boolean isCurrentModeActive() {
        // Null safety checks
        if (maceAura == null || stunSlam == null || attributeSwap == null) return false;
        
        boolean active = (currentMode == AuraMode.MaceAura && maceAura.isActive()) ||
               (currentMode == AuraMode.StunSlam && stunSlam.isActive()) ||
               (currentMode == AuraMode.AttributeSwap && attributeSwap.isActive());
               
        if (!active) {
            log("Current mode " + currentMode.name() + " is not active");
        }
        return active;
    }

    public AuraMode getCurrentMode() {
        return currentMode;
    }

    public String getLastDecisionReason() {
        return lastDecisionReason;
    }
    
    public boolean isStunSlamCurrentlyActive() {
        return currentMode == AuraMode.StunSlam && stunSlam != null && stunSlam.isSlamming();
    }
}