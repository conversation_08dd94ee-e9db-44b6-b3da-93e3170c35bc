package com.example.addon.utils;

import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.network.packet.Packet;
import net.minecraft.util.Identifier;

public class PacketUtils {

    public static void send(Packet<?> packet) {
        // This is a placeholder. The actual way to send a vanilla packet might be different.
        // For custom packets, you would use ClientPlayNetworking.send(Identifier, PacketByteBuf)
        // For vanilla packets, it's usually through MinecraftClient.getInstance().getNetworkHandler().sendPacket(packet);
        // However, the original error was about 'Packet' not being found, so let's try to ensure the import is correct.
        // If this still fails, we might need to re-evaluate how PlayerInteractEntityC2SPacket is sent.
        MinecraftClient.getInstance().getNetworkHandler().sendPacket(packet);
    }

    // Adding an overloaded method for PlayerInteractEntityC2SPacket specifically, if needed
    public static void send(PlayerInteractEntityC2SPacket packet) {
        MinecraftClient.getInstance().getNetworkHandler().sendPacket(packet);
    }
}