package com.example.addon.utils;

import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.Box;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import static meteordevelopment.meteorclient.MeteorClient.mc;

import java.util.Random;

/**
 * Utility class for randomized aiming calculations.
 * Provides methods to add natural-looking randomization to aiming,
 * with preference for points closer to the current mouse position.
 */
public class RandomizedAimingUtils {
    
    private static final Random random = new Random();
    
    /**
     * Calculate a randomized target position within an entity's bounding box.
     * Prefers points closer to the current crosshair position for more natural aiming.
     * 
     * @param entity The target entity
     * @param playerPos The player's eye position
     * @param randomizationStrength How much randomization to apply (0.0 = no randomization, 1.0 = full randomization)
     * @param proximityBias How much to bias towards points closer to current crosshair (0.0 = no bias, 1.0 = strong bias)
     * @return Randomized target position within the entity's bounds
     */
    public static Vec3d getRandomizedTargetPosition(Entity entity, Vec3d playerPos, double randomizationStrength, double proximityBias) {
        if (entity == null || playerPos == null) {
            return null;
        }
        
        Box boundingBox = entity.getBoundingBox();
        
        // Get current crosshair direction for proximity bias
        Vec3d currentLookDirection = getCurrentLookDirection();
        Vec3d entityCenter = boundingBox.getCenter();
        
        // Generate multiple candidate positions and choose the best one based on proximity bias
        int candidateCount = Math.max(1, (int)(proximityBias * 10) + 1);
        Vec3d bestPosition = null;
        double bestScore = Double.MAX_VALUE;
        
        for (int i = 0; i < candidateCount; i++) {
            // Generate random position within bounding box
            double x = lerp(boundingBox.minX, boundingBox.maxX, random.nextDouble());
            double y = lerp(boundingBox.minY, boundingBox.maxY, random.nextDouble());
            double z = lerp(boundingBox.minZ, boundingBox.maxZ, random.nextDouble());
            
            Vec3d candidatePos = new Vec3d(x, y, z);
            
            // Calculate score based on proximity to current look direction
            Vec3d directionToCandidate = candidatePos.subtract(playerPos).normalize();
            double dotProduct = currentLookDirection.dotProduct(directionToCandidate);
            double score = 1.0 - dotProduct; // Lower score = better (closer to current look direction)
            
            if (score < bestScore) {
                bestScore = score;
                bestPosition = candidatePos;
            }
        }
        
        // Apply randomization strength
        if (randomizationStrength < 1.0) {
            // Blend between center and randomized position
            bestPosition = entityCenter.lerp(bestPosition, randomizationStrength);
        }
        
        return bestPosition;
    }
    
    /**
     * Calculate a randomized target position with smart body part targeting.
     * Prefers head/torso areas for more realistic aiming patterns.
     * 
     * @param entity The target entity
     * @param playerPos The player's eye position
     * @param randomizationStrength How much randomization to apply
     * @param proximityBias How much to bias towards current crosshair position
     * @param preferVitalAreas Whether to prefer head/torso areas
     * @return Randomized target position with smart targeting
     */
    public static Vec3d getSmartRandomizedTargetPosition(Entity entity, Vec3d playerPos, double randomizationStrength, double proximityBias, boolean preferVitalAreas) {
        if (entity == null || playerPos == null) {
            return getRandomizedTargetPosition(entity, playerPos, randomizationStrength, proximityBias);
        }
        
        Box boundingBox = entity.getBoundingBox();
        Vec3d entityCenter = boundingBox.getCenter();
        
        if (!preferVitalAreas) {
            return getRandomizedTargetPosition(entity, playerPos, randomizationStrength, proximityBias);
        }
        
        // Define vital areas (head and torso)
        double entityHeight = boundingBox.maxY - boundingBox.minY;
        double headY = boundingBox.maxY - (entityHeight * 0.15); // Top 15% is head
        double torsoMinY = boundingBox.minY + (entityHeight * 0.25); // Bottom 25% to 85% is torso
        double torsoMaxY = boundingBox.maxY - (entityHeight * 0.15);
        
        Vec3d targetPos;
        
        // 70% chance to aim for torso, 20% for head, 10% for legs
        double targetChoice = random.nextDouble();
        
        if (targetChoice < 0.2) {
            // Head area
            double x = lerp(boundingBox.minX, boundingBox.maxX, random.nextDouble());
            double y = lerp(headY, boundingBox.maxY, random.nextDouble());
            double z = lerp(boundingBox.minZ, boundingBox.maxZ, random.nextDouble());
            targetPos = new Vec3d(x, y, z);
        } else if (targetChoice < 0.9) {
            // Torso area
            double x = lerp(boundingBox.minX, boundingBox.maxX, random.nextDouble());
            double y = lerp(torsoMinY, torsoMaxY, random.nextDouble());
            double z = lerp(boundingBox.minZ, boundingBox.maxZ, random.nextDouble());
            targetPos = new Vec3d(x, y, z);
        } else {
            // Legs area
            double x = lerp(boundingBox.minX, boundingBox.maxX, random.nextDouble());
            double y = lerp(boundingBox.minY, torsoMinY, random.nextDouble());
            double z = lerp(boundingBox.minZ, boundingBox.maxZ, random.nextDouble());
            targetPos = new Vec3d(x, y, z);
        }
        
        // Apply proximity bias
        if (proximityBias > 0.0) {
            Vec3d currentLookDirection = getCurrentLookDirection();
            Vec3d directionToTarget = targetPos.subtract(playerPos).normalize();
            double alignment = currentLookDirection.dotProduct(directionToTarget);
            
            // If the target is not well-aligned with current look direction, blend towards center
            if (alignment < 0.8) {
                double blendFactor = (1.0 - alignment) * proximityBias * 0.5;
                targetPos = targetPos.lerp(entityCenter, blendFactor);
            }
        }
        
        // Apply randomization strength
        if (randomizationStrength < 1.0) {
            targetPos = entityCenter.lerp(targetPos, randomizationStrength);
        }
        
        return targetPos;
    }
    
    /**
     * Add subtle randomization to existing rotation calculations.
     * This can be used to add small random offsets to existing aiming systems.
     * 
     * @param yaw Current yaw
     * @param pitch Current pitch
     * @param randomizationAmount Maximum random offset in degrees
     * @return Array containing [randomized_yaw, randomized_pitch]
     */
    public static float[] addRandomizationToRotation(float yaw, float pitch, double randomizationAmount) {
        if (randomizationAmount <= 0.0) {
            return new float[]{yaw, pitch};
        }
        
        // Add random offset within the specified range
        float yawOffset = (float)((random.nextDouble() - 0.5) * 2.0 * randomizationAmount);
        float pitchOffset = (float)((random.nextDouble() - 0.5) * 2.0 * randomizationAmount);
        
        return new float[]{yaw + yawOffset, pitch + pitchOffset};
    }
    
    /**
     * Get the current look direction of the player.
     * 
     * @return Normalized vector representing current look direction
     */
    private static Vec3d getCurrentLookDirection() {
        if (mc.player == null) {
            return new Vec3d(0, 0, 1); // Default forward direction
        }
        
        float yaw = mc.player.getYaw();
        float pitch = mc.player.getPitch();
        
        // Convert yaw and pitch to direction vector
        double yawRad = Math.toRadians(yaw);
        double pitchRad = Math.toRadians(pitch);
        
        double x = -Math.sin(yawRad) * Math.cos(pitchRad);
        double y = -Math.sin(pitchRad);
        double z = Math.cos(yawRad) * Math.cos(pitchRad);
        
        return new Vec3d(x, y, z).normalize();
    }
    
    /**
     * Linear interpolation between two values.
     * 
     * @param a Start value
     * @param b End value
     * @param t Interpolation factor (0.0 to 1.0)
     * @return Interpolated value
     */
    private static double lerp(double a, double b, double t) {
        return a + (b - a) * t;
    }
    
    /**
     * Generate a random offset within a circular pattern.
     * Useful for creating natural mouse movement patterns.
     * 
     * @param maxRadius Maximum radius of the circular offset
     * @return Vec3d representing the random offset (x, y components used for 2D offset)
     */
    public static Vec3d getCircularRandomOffset(double maxRadius) {
        double angle = random.nextDouble() * 2 * Math.PI;
        double radius = random.nextDouble() * maxRadius;
        
        double x = Math.cos(angle) * radius;
        double y = Math.sin(angle) * radius;
        
        return new Vec3d(x, y, 0);
    }
}
