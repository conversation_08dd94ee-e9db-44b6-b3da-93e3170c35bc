package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.item.ItemStack;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.meteorclient.events.world.TickEvent;
import net.minecraft.screen.slot.SlotActionType;

public class ElytraSwap extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    
    private final Setting<Double> swapDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-delay")
        .description("Delay in ticks after swapping to the target item.")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );

    private final Setting<Double> interactDelay = sgGeneral.add(new DoubleSetting.Builder()
        .name("interact-delay")
        .description("Delay in ticks after interacting with the item.")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );

    private final Setting<Double> swapBackDelaySetting = sgGeneral.add(new DoubleSetting.Builder()
        .name("swap-back-delay")
        .description("Delay in ticks before swapping back to the original slot.")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMin(0.0)
        .build()
    );
    
    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    public ElytraSwap() {
        super(AddonTemplate.CATEGORY, "elytra-swap", "Equips an elytra or chestplate from your hotbar.");
    }

    private int timer = -1;
    private int state = 0;
    private FindItemResult targetItem;
    private int originalHotbarSlot = -1;

    @Override
    public void onActivate() {
        ItemStack equippedChest = mc.player.getInventory().getStack(38); // 38 is the chest slot in the player inventory

        if (equippedChest.getItem() == Items.ELYTRA) {
            // Elytra is equipped, look for a chestplate
            targetItem = InvUtils.findInHotbar(itemStack -> itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
                itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
                itemStack.getItem() == Items.IRON_CHESTPLATE ||
                itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
                itemStack.getItem() == Items.LEATHER_CHESTPLATE);
        } else if (equippedChest.getItem() == Items.DIAMOND_CHESTPLATE ||
                   equippedChest.getItem() == Items.NETHERITE_CHESTPLATE ||
                   equippedChest.getItem() == Items.IRON_CHESTPLATE ||
                   equippedChest.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                   equippedChest.getItem() == Items.GOLDEN_CHESTPLATE ||
                   equippedChest.getItem() == Items.LEATHER_CHESTPLATE) {
            // Chestplate is equipped, look for an elytra
            targetItem = InvUtils.findInHotbar(Items.ELYTRA);
        } else {
            // Nothing relevant equipped, prioritize elytra then chestplate
            targetItem = InvUtils.findInHotbar(Items.ELYTRA);
            if (!targetItem.found()) {
                targetItem = InvUtils.findInHotbar(itemStack -> itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
                    itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
                    itemStack.getItem() == Items.IRON_CHESTPLATE ||
                    itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                    itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
                    itemStack.getItem() == Items.LEATHER_CHESTPLATE);
            }
        }

        if (targetItem.found()) {
            FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            originalHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
            state = 1;
            timer = swapDelay.get().intValue();
        } else {
            warning("No suitable item found in hotbar.");
            toggle();
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }
        
        if (timer > 0) {
            timer--;
        } else {
            if (state == 1) {
                // Swap to target item
                InvUtils.swap(targetItem.slot(), false);
                state = 2;
                timer = interactDelay.get().intValue();
            } else if (state == 2) {
                // Interact with item (equip)
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                state = 3;
                timer = swapBackDelaySetting.get().intValue();
            } else if (state == 3) {
                // Swap back to original slot
                InvUtils.swap(originalHotbarSlot, false);
                state = 0;
                timer = -1;
                originalHotbarSlot = -1;
                toggle();
            }
        }
    }
}