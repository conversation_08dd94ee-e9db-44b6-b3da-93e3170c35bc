package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.AimbotUtils;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.ModuleCommunication;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.entity.Entity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.tag.ItemTags;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import net.minecraft.client.MinecraftClient;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class Autoclicker extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Boolean> onlyWhileLMBHeld = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-LMB-held")
        .description("Only active while Left Mouse Button is held.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> clickDelay = sgGeneral.add(new IntSetting.Builder()
        .name("click-delay")
        .description("Delay between clicks in ticks (20 ticks = 1 second).")
        .defaultValue(1)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> prioritizeCrit = sgGeneral.add(new BoolSetting.Builder()
        .name("prioritize-crit")
        .description("Wait for optimal critical hit conditions before attacking.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> strictCritMode = sgGeneral.add(new BoolSetting.Builder()
        .name("strict-crit-mode")
        .description("Strict mode ensures perfect crit conditions. Lenient mode prioritizes attack speed.")
        .defaultValue(true)
        .visible(() -> prioritizeCrit.get())
        .build()
    );

    private final Setting<Integer> critWaitTicks = sgGeneral.add(new IntSetting.Builder()
        .name("crit-wait-ticks")
        .description("Maximum ticks to wait for optimal crit conditions before attacking anyway.")
        .defaultValue(5)
        .min(1)
        .max(20)
        .sliderMax(20)
        .visible(() -> prioritizeCrit.get())
        .build()
    );

    private final Setting<Boolean> allowBlockBreaking = sgGeneral.add(new BoolSetting.Builder()
        .name("allow-block-breaking")
        .description("Allow autoclicker to break blocks. When disabled, only attacks entities.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> blockBreakWaitTime = sgGeneral.add(new IntSetting.Builder()
        .name("block-break-wait-time")
        .description("Time to wait (in milliseconds) before allowing block breaking when first targeting a block. Prevents accidental mining.")
        .defaultValue(500)
        .min(0)
        .max(5000)
        .sliderMax(5000)
        .visible(() -> allowBlockBreaking.get())
        .build()
    );

    private final Setting<Boolean> blockObstructedAttacks = sgGeneral.add(new BoolSetting.Builder()
        .name("block-obstructed-attacks")
        .description("Block all left-click attacks (including manual) on blocks. Prevents breaking any blocks.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> perWeaponMinCharge = sgGeneral.add(new BoolSetting.Builder()
        .name("per-weapon-min-charge")
        .description("Enable separate minimum charge settings for different weapons.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> minCharge = sgGeneral.add(new DoubleSetting.Builder()
        .name("min-charge")
        .description("Minimum charge for autoclicker (ignored if 'Per Weapon Min Charge' is enabled).")
        .defaultValue(0.0)
        .min(0.0)
        .sliderMax(1.0)
        .visible(() -> !perWeaponMinCharge.get())
        .build()
    );

    private final Setting<Double> swordMinCharge = sgGeneral.add(new DoubleSetting.Builder()
        .name("sword-min-charge")
        .description("Minimum charge when holding a sword.")
        .defaultValue(0.8)
        .min(0.0)
        .sliderMax(1.0)
        .visible(() -> perWeaponMinCharge.get())
        .build()
    );

    private final Setting<Double> axeMinCharge = sgGeneral.add(new DoubleSetting.Builder()
        .name("axe-min-charge")
        .description("Minimum charge when holding an axe.")
        .defaultValue(0.9)
        .min(0.0)
        .sliderMax(1.0)
        .visible(() -> perWeaponMinCharge.get())
        .build()
    );

    private final Setting<Double> maceMinCharge = sgGeneral.add(new DoubleSetting.Builder()
        .name("mace-min-charge")
        .description("Minimum charge when holding a mace.")
        .defaultValue(0.7)
        .min(0.0)
        .sliderMax(1.0)
        .visible(() -> perWeaponMinCharge.get())
        .build()
    );

    private enum AttackMode {
        Packet,
        KeyPress
    }
    
    private final Setting<AttackMode> attackMode = sgGeneral.add(new EnumSetting.Builder<AttackMode>()
        .name("attack-mode")
        .description("The method used to attack targets. KeyPress is more anti-cheat compliant.")
        .defaultValue(AttackMode.KeyPress)
        .build()
    );
    
    // --- Fields for click timing, crit detection, and logging control ---
    private int ticksSinceLastClick = 0;
    private int ticksWaitingForCrit = 0;
    private long blockTargetStartTime = 0;
    private double previousY = 0.0;
    private double previousVelocityY = 0.0;
    private boolean wasAirborne = false;
    private long lastLogTime = 0;
    private final long LOG_RATE_LIMIT_MS = 100;
    private String lastLoggedMessageContent = "";
    // Critical hit physics threshold as per Minecraft mechanics
    private static final double CRIT_VELOCITY_THRESHOLD = -0.0784;
    
    // New fields for improved crit detection
    private boolean isFalling = false;
    private int fallTicks = 0;
    private static final int FALL_WAIT_TICKS = 2; // Wait for 2 ticks after starting to fall
    // --- End new fields ---
    
    // Add a field for the attack method reflection
    private Method doAttackMethod = null;
    
    public Autoclicker() {
        super(AddonTemplate.CATEGORY, "autoclicker", "Acts as a triggerbot, clicking automatically.");
        
        try {
            // Get the doAttack method via reflection
            doAttackMethod = MinecraftClient.class.getDeclaredMethod("method_1536"); // Obfuscated name for doAttack()
            doAttackMethod.setAccessible(true);
        } catch (NoSuchMethodException e) {
            error("Failed to find Minecraft's attack method via reflection. KeyPress mode may not work.");
            e.printStackTrace();
        }
    }

    private void logAutoclickerDebug(String message) {
        long currentTime = System.currentTimeMillis();
        // Only log if the message content changes or enough time has passed
        if (!message.equals(lastLoggedMessageContent) || (currentTime - lastLogTime > LOG_RATE_LIMIT_MS)) {
            System.out.println("Autoclicker DEBUG: " + message);
            lastLoggedMessageContent = message;
            lastLogTime = currentTime;
        }
    }

    private boolean canCriticalHit() {
        if (mc.player == null) return false;
        
        // Check if prioritize crit is enabled
        if (!prioritizeCrit.get()) {
            return true; // If not prioritizing crits, always allow attacking
        }
        
        // Get current Y velocity
        double currentVelocityY = mc.player.getVelocity().y;
        
        // Critical hit detection using the exact Minecraft physics threshold:
        // A critical hit occurs when the player's negative Y velocity is strictly less than -0.0784
        boolean hasOptimalCritVelocity = currentVelocityY < CRIT_VELOCITY_THRESHOLD;
        
        // Basic conditions for critical hits in Minecraft:
        boolean isOnGround = mc.player.isOnGround();
        boolean isInLiquid = mc.player.isInLava() || mc.player.isSubmergedInWater();
        boolean isClimbing = mc.player.isClimbing();
        boolean hasBlindness = mc.player.hasStatusEffect(net.minecraft.entity.effect.StatusEffects.BLINDNESS);
        boolean isRiding = mc.player.hasVehicle();
        
        // Check if basic crit conditions are met (not on ground, not in liquid, etc.)
        boolean basicCritConditions = !isOnGround && !isInLiquid && !isClimbing && !hasBlindness && !isRiding;
        
        // Determine if we should wait for better crit conditions
        boolean shouldWaitForCrit = shouldWaitForCriticalHit(basicCritConditions, hasOptimalCritVelocity, currentVelocityY);
        
        if (shouldWaitForCrit) {
            logAutoclickerDebug("Waiting for better crit conditions. Current velocity: " + String.format("%.4f", currentVelocityY));
            return false; // Wait for better conditions
        }
        
        // Allow attack if we're not waiting or if we have good conditions
        if (basicCritConditions && hasOptimalCritVelocity) {
            logAutoclickerDebug("Perfect crit conditions met - attacking with velocity: " + String.format("%.4f", currentVelocityY));
            ticksWaitingForCrit = 0; // Reset wait timer
        } else if (basicCritConditions) {
            logAutoclickerDebug("Basic crit conditions met, sub-optimal velocity - attacking anyway: " + String.format("%.4f", currentVelocityY));
            ticksWaitingForCrit = 0; // Reset wait timer
        } else {
            logAutoclickerDebug("No crit conditions possible - attacking normally");
            ticksWaitingForCrit = 0; // Reset wait timer
        }
        
        return true;
    }
    
    /**
     * Determines whether to wait for a critical hit or attack immediately
     * 
     * Crit Scenario: Wait for fall -> Crit attack -> Wait for recharge -> Normal attack
     * Normal Scenario: Wait for recharge -> Normal attack -> Wait for recharge -> Normal attack
     * 
     * @param aps Attacks Per Second of the weapon
     * @param timeToFall Time in seconds until player has enough downward velocity for crit
     * @return true if should wait for crit, false if should attack immediately
     */
    private boolean shouldWaitForCrit(double aps, double timeToFall) {
        // Constants
        final double CRIT_DAMAGE_MULTIPLIER = 1.5;
        final double NORMAL_DAMAGE_MULTIPLIER = 1.0;
        
        // Time between attacks in seconds
        double timeBetweenAttacks = 1.0 / aps;
        
        // Scenario A: Wait to fall, crit attack, wait for recharge, normal attack
        double timeForCritScenario = timeToFall + timeBetweenAttacks;
        double damageFromCritScenario = CRIT_DAMAGE_MULTIPLIER + NORMAL_DAMAGE_MULTIPLIER; // 2.5
        
        // Scenario B: Wait for recharge, normal attack, wait for recharge, normal attack  
        double timeForDoubleNormal = 2.0 * timeBetweenAttacks;
        double damageFromDoubleNormal = 2.0 * NORMAL_DAMAGE_MULTIPLIER; // 2.0
        
        // Calculate DPS for each scenario
        double critScenarioDPS = damageFromCritScenario / timeForCritScenario;
        double doubleNormalDPS = damageFromDoubleNormal / timeForDoubleNormal;
        
        // Return true if crit scenario has better DPS
        return critScenarioDPS > doubleNormalDPS;
    }
    
    /**
     * Returns detailed analysis of both scenarios
     */
    private DecisionAnalysis analyzeDecision(double aps, double timeToFall) {
        final double CRIT_DAMAGE_MULTIPLIER = 1.5;
        final double NORMAL_DAMAGE_MULTIPLIER = 1.0;
        
        double timeBetweenAttacks = 1.0 / aps;
        
        // Crit scenario
        double critTime = timeToFall + timeBetweenAttacks;
        double critDamage = CRIT_DAMAGE_MULTIPLIER + NORMAL_DAMAGE_MULTIPLIER;
        double critDPS = critDamage / critTime;
        
        // Normal scenario
        double normalTime = 2.0 * timeBetweenAttacks;
        double normalDamage = 2.0 * NORMAL_DAMAGE_MULTIPLIER;
        double doubleNormalDPS = normalDamage / normalTime;
        
        boolean shouldWait = critDPS > doubleNormalDPS;
        
        return new DecisionAnalysis(shouldWait, critDPS, doubleNormalDPS, critTime, normalTime, critDamage, normalDamage);
    }
    
    /**
     * Finds the breakeven point where both scenarios have equal DPS
     */
    private double findBreakevenFallTime(double aps) {
        // Set critDPS = normalDPS and solve for timeToFall
        // 2.5 / (timeToFall + 1/aps) = aps
        // 2.5 = aps * (timeToFall + 1/aps)
        // 2.5 = aps * timeToFall + 1
        // 1.5 = aps * timeToFall
        // timeToFall = 1.5 / aps
        
        return 1.5 / aps;
    }
    
    /**
     * Helper class to store analysis results
     */
    private static class DecisionAnalysis {
        public final boolean shouldWaitForCrit;
        public final double critDPS;
        public final double normalDPS;
        public final double critTime;
        public final double normalTime;
        public final double critDamage;
        public final double normalDamage;
        
        public DecisionAnalysis(boolean shouldWait, double critDPS, double normalDPS, 
                              double critTime, double normalTime, double critDamage, double normalDamage) {
            this.shouldWaitForCrit = shouldWait;
            this.critDPS = critDPS;
            this.normalDPS = normalDPS;
            this.critTime = critTime;
            this.normalTime = normalTime;
            this.critDamage = critDamage;
            this.normalDamage = normalDamage;
        }
        
        @Override
        public String toString() {
            return String.format("Decision: %s | Crit: %.2f DPS (%.1f dmg in %.2fs) | Normal: %.2f DPS (%.1f dmg in %.2fs)",
                shouldWaitForCrit ? "WAIT" : "ATTACK NOW", 
                critDPS, critDamage, critTime,
                normalDPS, normalDamage, normalTime);
        }
    }
    
    /**
     * Determines if we should wait for better critical hit conditions.
     * This is the core intelligence of crit prioritization.
     */
    private boolean shouldWaitForCriticalHit(boolean basicCritConditions, boolean hasOptimalCritVelocity, double currentVelocityY) {
        // If we already have perfect crit conditions, don't wait
        if (basicCritConditions && hasOptimalCritVelocity) {
            return false;
        }
        
        // If basic crit conditions aren't possible, don't wait
        if (!basicCritConditions) {
            return false;
        }
        
        // Update falling state
        updateFallingState();
        
        // Get current item attack speed based on item type (as per specification)
        double aps = getItemAttackSpeed();
        
        // If we're in strict mode and don't have optimal velocity, consider waiting
        if (strictCritMode.get() && !hasOptimalCritVelocity) {
            // Check if we should wait for fall
            if (isFalling && fallTicks < FALL_WAIT_TICKS) {
                logAutoclickerDebug("STRICT: Waiting for fall. Fall ticks: " + fallTicks + "/" + FALL_WAIT_TICKS);
                return true; // Wait for fall
            }
            
            // Calculate time to fall in seconds (20 ticks = 1 second)
            double timeToFall = (double) fallTicks / 20.0;
            
            // Use the DPS-based decision making
            boolean shouldWaitForCrit = shouldWaitForCrit(aps, timeToFall);
            
            if (shouldWaitForCrit && ticksWaitingForCrit < critWaitTicks.get()) {
                logAutoclickerDebug("STRICT: Waiting for better crit based on DPS analysis. APS: " + String.format("%.2f", aps) + ", Time to fall: " + String.format("%.3f", timeToFall));
                return true; // Wait for better velocity
            }
        }
        
        // In lenient mode, only wait if we're clearly going to get much better conditions
        if (!strictCritMode.get() && basicCritConditions && !hasOptimalCritVelocity) {
            // Only wait if we're early in a fall and likely to reach optimal velocity
            boolean earlyInFall = Math.abs(currentVelocityY) < Math.abs(CRIT_VELOCITY_THRESHOLD) * 0.5; // Less than half optimal
            boolean stillAccelerating = currentVelocityY < 0 && mc.player.fallDistance < 2.0; // Recent fall start
            
            if (earlyInFall && stillAccelerating && ticksWaitingForCrit < critWaitTicks.get() / 2) {
                logAutoclickerDebug("LENIENT: Early in fall, waiting briefly for better velocity. Current: " + String.format("%.4f", currentVelocityY));
                return true; // Wait briefly for better conditions
            }
        }
        
        return false; // Don't wait, attack now
    }
    
    /**
     * Gets the attack speed of the current item in hand based on item type
     * As per specification: Attack speed values must be determined based on the current item in the player's main hand
     * Implement type-based defaults: Swords (1.6 APS), Axes (1.0 APS), Mace (1.0 APS), Stick (2.0 APS), and other items (1.0 APS default)
     * 
     * @return Attacks Per Second (APS) of the current item
     */
    private double getItemAttackSpeed() {
        if (mc.player == null) return 1.0;
        
        ItemStack mainHandStack = mc.player.getMainHandStack();
        if (mainHandStack == null || mainHandStack.isEmpty()) {
            return 1.0; // Default attack speed
        }
        
        // Get the attack speed based on item type as per specification
        if (mainHandStack.isIn(ItemTags.SWORDS)) {
            return 1.6; // Swords typically have 1.6 APS
        } else if (mainHandStack.isIn(ItemTags.AXES)) {
            return 1.0; // Axes typically have 1.0 APS
        } else if (mainHandStack.getItem() == Items.MACE) {
            return 1.0; // Mace typically has 1.0 APS
        } else if (mainHandStack.getItem() == Items.STICK) {
            return 2.0; // Stick has 2.0 APS
        } else {
            return 1.0; // Default for all other items
        }
    }
    
    /**
     * Updates the falling state and tick counter
     */
    private void updateFallingState() {
        if (mc.player == null) return;
        
        boolean currentlyFalling = !mc.player.isOnGround() && mc.player.getVelocity().y < 0;
        
        if (currentlyFalling && !isFalling) {
            // Just started falling
            isFalling = true;
            fallTicks = 0;
            logAutoclickerDebug("Started falling");
        } else if (isFalling && currentlyFalling) {
            // Continuing to fall
            fallTicks++;
            logAutoclickerDebug("Falling for " + fallTicks + " ticks");
        } else if (isFalling && !currentlyFalling) {
            // Stopped falling (landed)
            isFalling = false;
            fallTicks = 0;
            logAutoclickerDebug("Stopped falling");
        } else if (!isFalling && !currentlyFalling) {
            // Not falling and wasn't falling
            fallTicks = 0;
        }
    }

    @Override
    public void onActivate() {
        super.onActivate();
        logAutoclickerDebug("Autoclicker activated.");
        ticksSinceLastClick = 0;
        ticksWaitingForCrit = 0;
        blockTargetStartTime = 0;
        isFalling = false;
        fallTicks = 0;
        if (mc.player != null) {
            previousY = mc.player.getY();
            previousVelocityY = mc.player.getVelocity().y;
            wasAirborne = !mc.player.isOnGround();
        }
        // Ensure the attack key is not stuck pressed from a previous run
        if (mc.options != null && mc.options.attackKey != null) {
            KeyBinding.setKeyPressed(mc.options.attackKey.getDefaultKey(), false);
        }
    }

    // Change from TickEvent.Post to TickEvent.Pre to match MaceAura
    @EventHandler
    private void onTick(TickEvent.Pre event) {
        // Initial comprehensive null check for critical game objects
        if (mc.player == null || mc.world == null || mc.interactionManager == null || mc.options == null || mc.options.attackKey == null) {
            logAutoclickerDebug("Player, world, interaction manager, options, or attack key is null, returning.");
            return;
        }

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            logAutoclickerDebug("Another module has already taken action this tick. Skipping Autoclicker.");
            return;
        }

        // Increment tick counter
        ticksSinceLastClick++;

        boolean shouldClick = true; // Assume we should click unless a condition prevents it

        // Check if onlyWhileLMBHeld is active and LMB is not held
        if (onlyWhileLMBHeld.get()) {
            boolean lmbIsHeld = InputCheckUtils.isLMBHeld();
            if (!lmbIsHeld) {
                shouldClick = false; // Prevent clicking if LMB not held
                logAutoclickerDebug("Only while LMB held is active and LMB is not held.");
            } else {
                logAutoclickerDebug("LMB held, proceeding with autoclicker logic.");
            }
        } else {
             logAutoclickerDebug("Only while LMB held is inactive, proceeding with autoclicker logic.");
        }

        // Check if enough time has passed since last click
        if (ticksSinceLastClick < clickDelay.get()) {
            shouldClick = false;
            logAutoclickerDebug("Click delay not met. Ticks since last click: " + ticksSinceLastClick + ", required: " + clickDelay.get());
        }

        // Check critical hit conditions with timeout logic
        boolean critConditionsMet = canCriticalHit();
        if (prioritizeCrit.get() && !critConditionsMet) {
            ticksWaitingForCrit++;
            
            // If we've been waiting too long, allow attack anyway to maintain responsiveness
            if (ticksWaitingForCrit >= critWaitTicks.get()) {
                logAutoclickerDebug("Crit wait timeout reached (" + ticksWaitingForCrit + " ticks). Attacking anyway for responsiveness.");
                ticksWaitingForCrit = 0; // Reset the counter
                // Allow the attack to proceed by not setting shouldClick to false
            } else {
                shouldClick = false;
                logAutoclickerDebug("Waiting for better crit conditions. Ticks waiting: " + ticksWaitingForCrit + "/" + critWaitTicks.get());
            }
        } else if (prioritizeCrit.get() && critConditionsMet) {
            // Reset wait timer when crit conditions are met
            ticksWaitingForCrit = 0;
            logAutoclickerDebug("Critical hit conditions satisfied or timeout reached.");
        }

        double currentMinCharge = minCharge.get();
        // Determine min charge based on weapon if perWeaponMinCharge is enabled
        if (perWeaponMinCharge.get()) {
            ItemStack mainHandStack = mc.player.getMainHandStack();
            // IMPORTANT: Null check mainHandStack and check if it's empty BEFORE calling methods on it
            if (mainHandStack == null || mainHandStack.isEmpty()) {
                logAutoclickerDebug("Per-weapon enabled, but main hand is empty. Using global minCharge: " + String.format("%.2f", minCharge.get()));
                currentMinCharge = minCharge.get(); // Fallback if no item in hand
            } else if (mainHandStack.isIn(ItemTags.SWORDS)) {
                currentMinCharge = swordMinCharge.get();
                logAutoclickerDebug("Weapon: Sword, Current Min Charge: " + String.format("%.2f", currentMinCharge));
            } else if (mainHandStack.isIn(ItemTags.AXES)) {
                currentMinCharge = axeMinCharge.get();
                logAutoclickerDebug("Weapon: Axe, Current Min Charge: " + String.format("%.2f", currentMinCharge));
            } else if (mainHandStack.getItem() == Items.MACE) {
                currentMinCharge = maceMinCharge.get();
                logAutoclickerDebug("Weapon: Mace, Current Min Charge: " + String.format("%.2f", currentMinCharge));
            } else {
                logAutoclickerDebug("Per-weapon enabled, but no specific weapon tag match for item. Using global minCharge: " + String.format("%.2f", minCharge.get()));
                currentMinCharge = minCharge.get(); // Fallback if item is not a recognized weapon
            }
        } else {
            logAutoclickerDebug("Per-weapon disabled. Using global minCharge: " + String.format("%.2f", currentMinCharge));
        }

        double attackCooldown = mc.player.getAttackCooldownProgress(0.0F);
        logAutoclickerDebug("Attack Cooldown Progress: " + String.format("%.2f", attackCooldown) + ", Required Min Charge: " + String.format("%.2f", currentMinCharge));

        if (attackCooldown < currentMinCharge) {
            shouldClick = false; // Prevent clicking if attack cooldown not met
            logAutoclickerDebug("Attack cooldown not met.");
        }

        // Determine if we have a valid target (entity or block)
        boolean hasValidTarget = false;
        boolean isBlockTarget = false;
        HitResult hitResult = mc.crosshairTarget;
        
        if (hitResult != null) {
            if (hitResult.getType() == HitResult.Type.ENTITY) {
                Entity targetEntity = ((EntityHitResult) hitResult).getEntity();
                if (targetEntity != null && !targetEntity.equals(mc.player) && targetEntity.isAlive()) {
                    // Check if aimbot allows attacking this entity
                    if (AimbotUtils.shouldAutoclickerAttackEntity(targetEntity)) {
                        hasValidTarget = true;
                        blockTargetStartTime = 0; // Reset block timer when targeting entities
                        logAutoclickerDebug("Valid entity target found: " + targetEntity.getName().getString());
                    } else {
                        logAutoclickerDebug("Entity target found but blocked by Aimbot targeting system.");
                    }
                } else {
                    logAutoclickerDebug("Crosshair target is entity but null, self, or dead.");
                }
            } else if (hitResult.getType() == HitResult.Type.BLOCK) {
                isBlockTarget = true;
                BlockHitResult blockHit = (BlockHitResult) hitResult;
                BlockPos blockPos = blockHit.getBlockPos();
                
                // Check if aimbot allows attacking blocks
                if (AimbotUtils.shouldAutoclickerAttackBlock() && allowBlockBreaking.get()) {
                    long currentTime = System.currentTimeMillis();
                    
                    // Initialize timer on first block target
                    if (blockTargetStartTime == 0) {
                        blockTargetStartTime = currentTime;
                    }
                    
                    long timeWaitingMs = currentTime - blockTargetStartTime;
                    
                    // Only apply obstruction check if we've waited long enough
                    // This allows breaking blocks you're directly targeting immediately
                    // but prevents accidental breaking of blocks behind your target
                    if (timeWaitingMs >= blockBreakWaitTime.get()) {
                        // After wait period, allow breaking even if obstructed
                        // since the user has intentionally targeted the block for this long
                        hasValidTarget = true;
                        logAutoclickerDebug("Block breaking allowed after wait period. Time elapsed: " + timeWaitingMs + "/" + blockBreakWaitTime.get() + " ms.");
                    } else {
                        // During initial wait period, check for obstructions
                        // to prevent accidental breaking of blocks in front of target
                        if (RaycastUtils.shouldBlockBreakingBeBlocked(blockPos)) {
                            hasValidTarget = false;
                            blockTargetStartTime = 0; // Reset timer when obstructed
                            logAutoclickerDebug("Block target found but obstructed by another block. Cannot break through blocks during wait period.");
                        } else {
                            // No obstruction, allow building up the wait timer
                            hasValidTarget = false; // Still false during wait period
                            logAutoclickerDebug("Block targeted with no obstruction. Building up wait time (" + timeWaitingMs + "/" + blockBreakWaitTime.get() + " ms).");
                        }
                    }
                } else {
                    hasValidTarget = false;
                    blockTargetStartTime = 0; // Reset timer when block breaking is disabled or blocked by aimbot
                    if (!AimbotUtils.shouldAutoclickerAttackBlock()) {
                        logAutoclickerDebug("Block target found but blocked by Aimbot targeting system.");
                    } else {
                        logAutoclickerDebug("Block target found but block breaking is disabled.");
                    }
                }
            } else {
                blockTargetStartTime = 0; // Reset block timer for other target types
                logAutoclickerDebug("No valid entity or block target.");
            }
        } else {
            blockTargetStartTime = 0; // Reset block timer when no target
            logAutoclickerDebug("Crosshair target is null.");
        }

        // Final decision to perform a click
        if (shouldClick && hasValidTarget) {
            // Mark that this module is taking action this tick
            try {
                Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
                java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
                actionTakenField.setAccessible(true);
                actionTakenField.setBoolean(null, true);
            } catch (Exception e) {
                // Ignore if we can't set the flag
            }
            
            // Perform a single click using the selected attack mode
            if (hitResult.getType() == HitResult.Type.ENTITY) {
                // Attack entity
                Entity targetEntity = ((EntityHitResult) hitResult).getEntity();

                // Request sprint reset from W-Tap module for optimal PvP mechanics
                ModuleCommunication.requestSprintReset(targetEntity);
                logAutoclickerDebug("Requested sprint reset for entity attack on: " + targetEntity.getName().getString());

                if (attackMode.get() == AttackMode.KeyPress) {
                    // Use reflection to call Minecraft's doAttack method (more anti-cheat friendly)
                    if (doAttackMethod != null) {
                        try {
                            doAttackMethod.invoke(mc);
                            logAutoclickerDebug("Performed entity attack click using KeyPress mode.");
                        } catch (IllegalAccessException | InvocationTargetException e) {
                            error("Failed to invoke 'doAttack' method: " + e.getMessage());
                            // Fall back to direct method if reflection fails
                            mc.interactionManager.attackEntity(mc.player, targetEntity);
                            mc.player.swingHand(mc.player.getActiveHand());
                            logAutoclickerDebug("Fell back to packet mode after KeyPress failure.");
                        }
                    } else {
                        // Fall back to direct method if reflection setup failed
                        mc.interactionManager.attackEntity(mc.player, targetEntity);
                        mc.player.swingHand(mc.player.getActiveHand());
                        logAutoclickerDebug("Performed entity attack click using fallback Packet mode.");
                    }
                } else {
                    // Packet mode (original behavior)
                    mc.interactionManager.attackEntity(mc.player, targetEntity);
                    mc.player.swingHand(mc.player.getActiveHand());
                    logAutoclickerDebug("Performed entity attack click using Packet mode.");
                }
            } else if (hitResult.getType() == HitResult.Type.BLOCK) {
                // Attack/break block
                BlockHitResult blockHit = (BlockHitResult) hitResult;
                
                if (attackMode.get() == AttackMode.KeyPress) {
                    // Use reflection to call Minecraft's doAttack method
                    if (doAttackMethod != null) {
                        try {
                            doAttackMethod.invoke(mc);
                            logAutoclickerDebug("Performed block attack click using KeyPress mode.");
                        } catch (IllegalAccessException | InvocationTargetException e) {
                            error("Failed to invoke 'doAttack' method: " + e.getMessage());
                            // Fall back to direct method if reflection fails
                            mc.interactionManager.attackBlock(blockHit.getBlockPos(), blockHit.getSide());
                            mc.player.swingHand(mc.player.getActiveHand());
                            logAutoclickerDebug("Fell back to packet mode after KeyPress failure.");
                        }
                    } else {
                        // Fall back to direct method if reflection setup failed
                        mc.interactionManager.attackBlock(blockHit.getBlockPos(), blockHit.getSide());
                        mc.player.swingHand(mc.player.getActiveHand());
                        logAutoclickerDebug("Performed block attack click using fallback Packet mode.");
                    }
                } else {
                    // Packet mode (original behavior)
                    mc.interactionManager.attackBlock(blockHit.getBlockPos(), blockHit.getSide());
                    mc.player.swingHand(mc.player.getActiveHand());
                    logAutoclickerDebug("Performed block attack click using Packet mode.");
                }
                
                long waitTimeMs = System.currentTimeMillis() - blockTargetStartTime;
                logAutoclickerDebug("Performed block attack after " + waitTimeMs + " ms wait period.");
                blockTargetStartTime = 0; // Reset block timer after successful attack
            }
            
            // Reset the tick counter after successful click
            ticksSinceLastClick = 0;
        } else {
            if (!shouldClick) {
                logAutoclickerDebug("Conditions not met for clicking.");
            } else if (!hasValidTarget) {
                if (isBlockTarget && !allowBlockBreaking.get()) {
                    logAutoclickerDebug("Block target found but block breaking is disabled.");
                } else if (isBlockTarget && allowBlockBreaking.get()) {
                    // Check if it's due to obstruction or wait time
                    long timeWaitingMs = blockTargetStartTime > 0 ? System.currentTimeMillis() - blockTargetStartTime : 0;
                    if (timeWaitingMs >= blockBreakWaitTime.get()) {
                        // After wait period, obstruction doesn't matter
                        logAutoclickerDebug("Block target ready to break after wait period (" + timeWaitingMs + "/" + blockBreakWaitTime.get() + " ms).");
                    } else {
                        // During wait period, check obstruction
                        BlockHitResult blockHit = (BlockHitResult) hitResult;
                        if (RaycastUtils.shouldBlockBreakingBeBlocked(blockHit.getBlockPos())) {
                            logAutoclickerDebug("Block target found but obstructed by another block during wait period.");
                        } else {
                            logAutoclickerDebug("Block targeted but still in wait period (" + timeWaitingMs + "/" + blockBreakWaitTime.get() + " ms). Building up wait time.");
                        }
                    }
                } else {
                    logAutoclickerDebug("No valid target for clicking.");
                }
            }
        }

        // Update previous Y position for next tick
        previousY = mc.player.getY();
        previousVelocityY = mc.player.getVelocity().y;
        wasAirborne = !mc.player.isOnGround();
    }

    /**
     * Add a method to check if another module has taken action this tick
     * @return true if another module has already taken action this tick
     */
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }
    
    /**
     * Public getter for the block obstructed attacks setting.
     * Used by the mixin to determine if obstruction blocking is enabled.
     * 
     * @return true if obstructed attacks should be blocked, false otherwise
     */
    public boolean shouldBlockObstructedAttacks() {
        return blockObstructedAttacks.get();
    }

    @Override
    public void onDeactivate() {
        logAutoclickerDebug("Autoclicker deactivated.");
        // Ensure the attack key is not stuck pressed
        if (mc.options != null && mc.options.attackKey != null) {
            KeyBinding.setKeyPressed(mc.options.attackKey.getDefaultKey(), false);
        }
        super.onDeactivate();
    }
}