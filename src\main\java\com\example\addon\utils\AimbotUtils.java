package com.example.addon.utils;

import com.example.addon.modules.Aimbot;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.Vec3d;

/**
 * Utility class for Aimbot-related functionality
 */
public class AimbotUtils {
    private static final MinecraftClient mc = MinecraftClient.getInstance();

    /**
     * Checks if the autoclicker should attack the entity based on the aimbot's current target
     * @param targetEntity The entity that the autoclicker is considering attacking
     * @return true if the autoclicker should attack, false if it should be blocked
     */
    public static boolean shouldAutoclickerAttackEntity(Entity targetEntity) {
        // Get the aimbot module
        Aimbot aimbot = Modules.get().get(Aimbot.class);
        
        // If aimbot is not active, allow normal autoclicker behavior
        if (aimbot == null || !aimbot.isActive()) {
            return true;
        }
        
        // If aimbot is active but there's no current target, don't attack
        LivingEntity aimbotTarget = aimbot.getCurrentTarget();
        if (aimbotTarget == null) {
            return false;
        }
        
        // If aimbot is active and there's a target, only allow attacking that target
        // This prevents the autoclicker from attacking other entities or blocks when aimbot is active
        return targetEntity.equals(aimbotTarget);
    }
    
    /**
     * Checks if the autoclicker should attack a block based on the aimbot's state
     * @return true if the autoclicker should attack blocks, false if it should be blocked
     */
    public static boolean shouldAutoclickerAttackBlock() {
        // Get the aimbot module
        Aimbot aimbot = Modules.get().get(Aimbot.class);
        
        // If aimbot is not active, allow normal autoclicker behavior
        if (aimbot == null || !aimbot.isActive()) {
            return true;
        }
        
        // If aimbot is active and has a target, don't allow block breaking
        // This prevents the autoclicker from breaking blocks when aiming at an entity
        return aimbot.getCurrentTarget() == null;
    }
}