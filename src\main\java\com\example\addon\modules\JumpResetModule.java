package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.network.packet.s2c.play.EntityStatusS2CPacket;
import net.minecraft.network.packet.s2c.play.HealthUpdateS2CPacket;

import java.util.Random;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class JumpResetModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final Random random = new Random();

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("The delay in ticks before performing the jump reset.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Double> chance = sgGeneral.add(new DoubleSetting.Builder()
        .name("chance")
        .description("The percentage chance of performing the jump reset.")
        .defaultValue(100.0)
        .min(0.0)
        .max(100.0)
        .sliderMax(100.0)
        .build()
    );

    private final Setting<Boolean> packetBasedDetection = sgGeneral.add(new BoolSetting.Builder()
        .name("packet-based-detection")
        .description("Use packet-based detection for more immediate response.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> onlyOnGround = sgGeneral.add(new BoolSetting.Builder()
        .name("only-on-ground")
        .description("Only perform jump reset when on ground.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> jumpDuration = sgGeneral.add(new IntSetting.Builder()
        .name("jump-duration")
        .description("Duration in ticks to hold the jump key.")
        .defaultValue(1)
        .min(1)
        .max(5)
        .build()
    );

    private float lastHealth;
    private int jumpDelayTimer = -1;
    private int jumpKeyHoldTimer = 0;
    private boolean jumpKeyPressed = false;

    public JumpResetModule() {
        super(AddonTemplate.CATEGORY, "jump-reset", "Automatically jumps after being hit, with a configurable delay and chance.");
    }

    @Override
    public void onActivate() {
        lastHealth = mc.player != null ? mc.player.getHealth() : 20.0f;
        jumpDelayTimer = -1;
        jumpKeyHoldTimer = 0;
        jumpKeyPressed = false;
        ChatUtils.info("Jump Reset module activated.");
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null) return;

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }

        // Handle jump key release
        if (jumpKeyPressed && jumpKeyHoldTimer > 0) {
            jumpKeyHoldTimer--;
            if (jumpKeyHoldTimer <= 0) {
                KeyBinding.setKeyPressed(mc.options.jumpKey.getDefaultKey(), false);
                jumpKeyPressed = false;
            }
        }

        float currentHealth = mc.player.getHealth();

        // Health-based detection (fallback when packet-based detection is disabled or fails)
        if (!packetBasedDetection.get() && currentHealth < lastHealth) {
            // Player has taken damage
            if (random.nextDouble() * 100 <= chance.get()) {
                scheduleJump();
            }
        }
        
        lastHealth = currentHealth;

        // Handle jump delay timer
        if (jumpDelayTimer > 0) {
            jumpDelayTimer--;
        } else if (jumpDelayTimer == 0) {
            performJump();
            jumpDelayTimer = -1;
        }
    }

    @EventHandler
    private void onPacketReceive(PacketEvent.Receive event) {
        if (!packetBasedDetection.get() || mc.player == null) return;

        // Packet-based detection for more immediate response
        if (event.packet instanceof EntityStatusS2CPacket packet) {
            // Check if this is a damage packet for the local player
            if (mc.player != null && packet.getEntity(mc.world) == mc.player && packet.getStatus() == 2) {
                // Entity status 2 indicates damage
                if (random.nextDouble() * 100 <= chance.get()) {
                    scheduleJump();
                }
            }
        } else if (event.packet instanceof HealthUpdateS2CPacket packet) {
            // Health update packet - another way to detect damage
            float newHealth = packet.getHealth();
            if (newHealth < lastHealth) {
                if (random.nextDouble() * 100 <= chance.get()) {
                    scheduleJump();
                }
            }
            lastHealth = newHealth;
        }
    }

    private void scheduleJump() {
        jumpDelayTimer = delay.get();
    }

    private void performJump() {
        if (mc.player == null) return;

        // Check if we should only jump when on ground
        if (onlyOnGround.get() && !mc.player.isOnGround()) {
            return;
        }

        // Press the jump key
        markActionTaken(); // Notify action coordinator
        KeyBinding.setKeyPressed(mc.options.jumpKey.getDefaultKey(), true);
        jumpKeyPressed = true;
        jumpKeyHoldTimer = jumpDuration.get();

        ChatUtils.info("Jump Reset performed!");
    }

    @Override
    public void onDeactivate() {
        jumpDelayTimer = -1;
        jumpKeyHoldTimer = 0;
        
        // Release jump key if it's pressed
        if (jumpKeyPressed && mc.options != null && mc.options.jumpKey != null) {
            KeyBinding.setKeyPressed(mc.options.jumpKey.getDefaultKey(), false);
            jumpKeyPressed = false;
        }
        
        ChatUtils.info("Jump Reset module deactivated.");
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    // Mark that this module is taking action this tick
    private void markActionTaken() {
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            actionTakenField.setBoolean(null, true);
        } catch (Exception e) {
            // Ignore if we can't set the flag
        }
    }
}