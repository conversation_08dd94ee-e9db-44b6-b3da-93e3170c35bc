package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.NoiseUtils; // Added import for NoiseUtils
import com.example.addon.utils.RaycastUtils; // Added import for RaycastUtils
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.util.math.Vec3d;
import java.util.function.Predicate;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class Aimbot extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("The maximum range to aim at entities.")
        .defaultValue(4.5)
        .min(0.0)
        .sliderMax(10.0)
        .build()
    );

    private final Setting<Double> fov = sgGeneral.add(new DoubleSetting.Builder() // New FOV setting
        .name("fov")
        .description("The field of view (in degrees) within which to target entities.")
        .defaultValue(90.0)
        .min(0.0)
        .max(360.0)
        .sliderMin(0.0)
        .sliderMax(360.0)
        .build()
    );

    private final Setting<Double> aimSpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("The speed at which the player aims at the target.")
        .defaultValue(0.5d)
        .range(0.1d, 1.0d)
        .build()
    );

    // --- New setting for block obstruction prevention ---
    private final Setting<Boolean> blockObstructedAttacks = sgGeneral.add(new BoolSetting.Builder()
        .name("block-obstructed-attacks")
        .description("Block all left-click attacks (including manual) on entities when obstructed by blocks.")
        .defaultValue(true)
        .build()
    );
    // --- End new setting ---

    private final Setting<Boolean> onlyWhileLMBHeld = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-LMB-held")
        .description("Only active while Left Mouse Button is held.")
        .defaultValue(true)
        .build()
    );

    // --- Fields for logging control ---
    private long lastAnyLogTime = 0;
    private final long GLOBAL_LOG_RATE_LIMIT_MS = 100;

    private Vec3d lastLoggedPlayerPos = Vec3d.ZERO;
    private Vec3d lastLoggedTargetPos = Vec3d.ZERO;
    private float lastLoggedTargetYaw = 0;
    private float lastLoggedTargetPitch = 0;
    private String lastLoggedSimpleMessageContent = "";
    private boolean detailedLogInitialized = false; // <<< NEW: Flag to force first detailed log
    private final double POS_EPSILON = 0.05;
    private final float ANGLE_EPSILON = 0.5f;
    
    // --- Noise fields with fixed optimal values ---
    private long startTime = System.currentTimeMillis();
    private final double NOISE_SEED = Math.random() * 10000;
    
    // Using a simpler noise algorithm for more predictable behavior
    private static final double NOISE_AMPLITUDE = 0.2;
    private static final double NOISE_FREQUENCY = 0.05;
    // --- End noise fields ---

    // --- Fields for obstruction checking and target tracking ---
    private boolean isObstructed = false;
    private LivingEntity currentTarget = null;
    // --- End obstruction fields ---

    // --- End fields ---

    public Aimbot() {
        super(AddonTemplate.CATEGORY, "aimbot", "Automatically aims at entities.");
    }

    @Override
    public void onDeactivate() {
        super.onDeactivate();
        detailedLogInitialized = false; // Reset when module deactivates
        isObstructed = false; // Reset obstruction state
        currentTarget = null; // Reset target
    }

    // --- New method for obstruction checking ---
    public boolean shouldBlockObstructedAttacks() {
        return blockObstructedAttacks.get();
    }
    
    public boolean isObstructed() {
        return isObstructed;
    }
    
    public LivingEntity getCurrentTarget() {
        return currentTarget;
    }
    // --- End new method ---

    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || mc.world == null) {
            logSimpleDebug("Player or world is null, returning.");
            return;
        }

        if (onlyWhileLMBHeld.get()) {
            boolean lmbIsHeld = InputCheckUtils.isLMBHeld();
            if (lmbIsHeld) {
                logSimpleDebug("LMB check - 'onlyWhileLMBHeld' is true, InputCheckUtils says LMB is held: true");
            } else {
                currentTarget = null; // Reset target when LMB is not held
                return;
            }
        }

        LivingEntity target = findTarget();
        if (target != null) {
            currentTarget = target; // Store current target
            logSimpleDebug("Target found: " + target.getName().getString() + ". Proceeding to aim.");
            aimAtEntity(target, event);
        } else {
            logSimpleDebug("No target found.");
            detailedLogInitialized = false; // Reset if no target found to force new detailed log when target reappears
            isObstructed = false; // Reset obstruction state when no target
            currentTarget = null; // Reset target
        }
    }

    private LivingEntity findTarget() {
        Predicate<Entity> targetPredicate = entity -> {
            // EXCLUDE THE LOCAL PLAYER!
            if (entity.equals(mc.player)) {
                return false;
            }

            Targets targetsModule = Modules.get().get(Targets.class);
            if (targetsModule == null || !targetsModule.isActive()) {
                // If the Targets module is not active or not found, use a default behavior or skip targeting.
                // For now, we'll skip targeting if the Targets module isn't active.
                return false;
            }

            boolean typeMatches = targetsModule.entityFilter.get().contains(entity.getType());
            double distance = mc.player.distanceTo(entity);
            boolean withinRange = distance <= range.get();
            boolean isLiving = entity instanceof LivingEntity;
            
            // Check if entity is within FOV
            boolean withinFOV = true;
            if (fov.get() < 360.0) {
                withinFOV = isWithinFOV(entity);
            }
            
            return typeMatches && withinRange && isLiving && withinFOV;
        };

        Entity target = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

        if (target instanceof LivingEntity) {
            return (LivingEntity) target;
        }
        return null;
    }

    // New method to check if an entity is within the FOV
    private boolean isWithinFOV(Entity entity) {
        if (mc.player == null) return false;
        
        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = entity.getEyePos();
        
        double deltaX = targetPos.x - playerPos.x;
        double deltaY = targetPos.y - playerPos.y;
        double deltaZ = targetPos.z - playerPos.z;
        
        double distanceXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        // Calculate yaw and pitch to target
        float targetYaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(deltaY, distanceXZ));
        
        // Normalize angles
        targetYaw = normalizeAngle(targetYaw);
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);
        
        // Get current player angles
        float currentYaw = normalizeAngle(mc.player.getYaw());
        float currentPitch = mc.player.getPitch();
        
        // Calculate angle difference
        float yawDiff = Math.abs(targetYaw - currentYaw);
        if (yawDiff > 180.0F) {
            yawDiff = 360.0F - yawDiff;
        }
        
        float pitchDiff = Math.abs(targetPitch - currentPitch);
        
        // Check if within FOV
        return yawDiff <= fov.get() / 2.0 && pitchDiff <= fov.get() / 2.0;
    }
    
    // Helper method to normalize angles to [0, 360)
    private float normalizeAngle(float angle) {
        angle %= 360.0F;
        if (angle >= 0.0F) {
            return angle;
        } else {
            return 360.0F + angle;
        }
    }

    private void aimAtEntity(LivingEntity entity, Render3DEvent event) {
        logSimpleDebug("Entering aimAtEntity for target: " + entity.getName().getString());

        Vec3d playerPos = mc.player.getEyePos();
        
        // Aim at the center of the entity (eye position) for more accurate targeting
        Vec3d targetPos = entity.getEyePos();

        double x = targetPos.x - playerPos.x;
        double y = targetPos.y - playerPos.y;
        double z = targetPos.z - playerPos.z;

        double distanceXZ = Math.sqrt(x * x + z * z);

        float targetYaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(y, distanceXZ));

        // Check for block obstructions
        isObstructed = RaycastUtils.hasBlockObstruction(targetPos);
        if (isObstructed && blockObstructedAttacks.get()) {
            logSimpleDebug("Target is obstructed by blocks. Blocking attack.");
        }

        // Apply simpler noise algorithm for more predictable behavior
        long currentTime = System.currentTimeMillis();
        double time = (currentTime - startTime) / 1000.0; // Time in seconds
        
        // Generate simple sine wave noise
        double noiseX = Math.sin(time * NOISE_FREQUENCY + NOISE_SEED) * NOISE_AMPLITUDE;
        double noiseY = Math.cos(time * NOISE_FREQUENCY * 1.3 + NOISE_SEED) * NOISE_AMPLITUDE * 0.7;