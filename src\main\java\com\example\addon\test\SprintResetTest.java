package com.example.addon.test;

import com.example.addon.utils.ModuleCommunication;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;

/**
 * Simple test class to verify sprint reset communication functionality.
 * This can be used to manually test the communication between AutoClicker and W-Tap.
 */
public class SprintResetTest {
    
    /**
     * Test the basic sprint reset request and response cycle.
     */
    public static void testSprintResetCommunication() {
        System.out.println("=== Sprint Reset Communication Test ===");
        
        // Test 1: Initial state should be no request
        System.out.println("Test 1: Initial state");
        boolean initialRequest = ModuleCommunication.isSprintResetRequested();
        System.out.println("Initial request state: " + initialRequest + " (should be false)");
        
        // Test 2: Request sprint reset
        System.out.println("\nTest 2: Request sprint reset");
        // Note: We can't create a real entity in this test, so we'll use null
        ModuleCommunication.requestSprintReset(null);
        boolean afterRequest = ModuleCommunication.isSprintResetRequested();
        System.out.println("After request state: " + afterRequest + " (should be true)");
        
        // Test 3: Clear request
        System.out.println("\nTest 3: Clear request");
        ModuleCommunication.clearSprintResetRequest();
        boolean afterClear = ModuleCommunication.isSprintResetRequested();
        System.out.println("After clear state: " + afterClear + " (should be false)");
        
        // Test 4: Timeout test
        System.out.println("\nTest 4: Timeout test");
        ModuleCommunication.requestSprintReset(null);
        System.out.println("Request made, waiting for timeout...");
        
        // Simulate waiting longer than timeout
        try {
            Thread.sleep(150); // Wait longer than 100ms timeout
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        boolean afterTimeout = ModuleCommunication.isSprintResetRequested();
        System.out.println("After timeout state: " + afterTimeout + " (should be false due to timeout)");
        
        System.out.println("\n=== Sprint Reset Communication Test Complete ===");
    }
    
    /**
     * Test the target-specific sprint reset functionality.
     */
    public static void testTargetSpecificSprintReset() {
        System.out.println("=== Target-Specific Sprint Reset Test ===");
        
        // Clear any existing requests
        ModuleCommunication.clearSprintResetRequest();
        
        // Test with null target (simulating entity)
        System.out.println("Test 1: Request with null target");
        ModuleCommunication.requestSprintReset(null);
        Entity target = ModuleCommunication.getSprintResetTarget();
        System.out.println("Retrieved target: " + target + " (should be null)");
        
        boolean isForTarget = ModuleCommunication.isSprintResetForTarget(null);
        System.out.println("Is request for null target: " + isForTarget + " (should be true)");
        
        // Clean up
        ModuleCommunication.clearSprintResetRequest();
        
        System.out.println("\n=== Target-Specific Sprint Reset Test Complete ===");
    }
    
    /**
     * Run all tests.
     */
    public static void runAllTests() {
        testSprintResetCommunication();
        System.out.println();
        testTargetSpecificSprintReset();
    }
}
