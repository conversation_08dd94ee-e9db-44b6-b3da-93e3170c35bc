package com.example.addon.mixin;

import com.example.addon.modules.Autoclicker;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(MinecraftClient.class)
public class MinecraftClientMixin {
    
    @Inject(method = "doAttack", at = @At("HEAD"), cancellable = true)
    private void onDoAttack(CallbackInfoReturnable<Boolean> cir) {
        MinecraftClient mc = (MinecraftClient) (Object) this;
        
        // Get autoclicker module
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);
        if (autoclicker == null || !autoclicker.isActive()) {
            return; // Don't interfere if autoclicker is not active
        }
        
        // Check if block obstruction blocking is enabled
        if (!autoclicker.shouldBlockObstructedAttacks()) {
            return; // Don't block if setting is disabled
        }
        
        // Check what we're targeting
        HitResult hitResult = mc.crosshairTarget;
        if (hitResult == null) return;
        
        // Skip entity obstruction checking - only block breaking should be blocked
        // Entity attacks should go through even if obstructed
        
        // If we're targeting a block, block all block breaking when the setting is enabled
        if (hitResult.getType() == HitResult.Type.BLOCK) {
            // Cancel all block attacks when this setting is enabled
            cir.setReturnValue(false);
            // Optionally notify player
            if (mc.player != null) {
                mc.player.sendMessage(net.minecraft.text.Text.literal("§cBlock breaking blocked"), true);
            }
            return;
        }
    }
}