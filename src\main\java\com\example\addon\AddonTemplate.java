package com.example.addon;

import com.example.addon.commands.CommandExample;
import com.example.addon.hud.HudExample;
import com.example.addon.modules.AttributeSwap;
import com.example.addon.modules.ElytraSwap;
import com.example.addon.modules.MaceAura;
import com.example.addon.modules.StunSlam;
import com.example.addon.modules.Autoclicker;
import com.example.addon.modules.Aimbot;
import com.example.addon.modules.SilentAimTest;
import com.example.addon.modules.Targets;
import com.example.addon.modules.AutoAura;
import com.example.addon.modules.ActionCoordinator;
import com.example.addon.modules.WTapModule;
import com.example.addon.modules.JumpResetModule;
import com.example.addon.modules.ShieldBreaker;
import com.example.addon.modules.AutoWeb;
import com.example.addon.modules.PearlFollow;
import com.example.addon.modules.ModuleExample;
import com.mojang.logging.LogUtils;
import meteordevelopment.meteorclient.addons.GithubRepo;
import meteordevelopment.meteorclient.addons.MeteorAddon;
import meteordevelopment.meteorclient.commands.Commands;
import meteordevelopment.meteorclient.systems.hud.Hud;
import meteordevelopment.meteorclient.systems.hud.HudGroup;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Modules;
import org.slf4j.Logger;

public class AddonTemplate extends MeteorAddon {
    public static final Logger LOG = LogUtils.getLogger();
    public static final Category CATEGORY = new Category("Example");
    public static final HudGroup HUD_GROUP = new HudGroup("Example");

    @Override
    public void onInitialize() {
        LOG.info("Initializing Meteor Addon Template");

        // Modules
        Modules.get().add(new ModuleExample());
        Modules.get().add(new AttributeSwap());
        Modules.get().add(new ElytraSwap());
        Modules.get().add(new MaceAura());
        Modules.get().add(new StunSlam());
        Modules.get().add(new SilentAimTest());
        Modules.get().add(new Autoclicker());
        Modules.get().add(new Aimbot());
        Modules.get().add(new Targets());   
        Modules.get().add(new AutoAura());
        Modules.get().add(new ActionCoordinator());
        Modules.get().add(new WTapModule());
        Modules.get().add(new JumpResetModule());
        Modules.get().add(new ShieldBreaker());
        Modules.get().add(new AutoWeb());
        Modules.get().add(new PearlFollow());

        // Commands
        Commands.add(new CommandExample());

        // HUD
        Hud.get().register(HudExample.INFO);
    }

    @Override
    public void onRegisterCategories() {
        Modules.registerCategory(CATEGORY);
    }

    @Override
    public String getPackage() {
        return "com.example.addon";
    }

    @Override
    public GithubRepo getRepo() {
        return new GithubRepo("MeteorDevelopment", "meteor-addon-template");
    }
}