@echo off
setlocal

set /p commitMessage=Enter commit message: 

echo Building project...
call gradlew build
if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed, continuing...

set destPath=C:\Users\<USER>\AppData\Roaming\ModrinthApp\profiles\meter\mods\mace-1.0.0.jar
set sourcePath=build\libs\mace-1.0.0.jar

echo Checking for source jar...
if not exist "%sourcePath%" (
    echo ERROR: New jar not found in build\libs\
    pause
    exit /b 1
)

echo Source jar found!

if exist "%destPath%" (
    echo Old jar found, removing it...
    del "%destPath%"
) else (
    echo No existing jar found, proceeding...
)

echo Moving new jar...
move "%sourcePath%" "C:\Users\<USER>\AppData\Roaming\ModrinthApp\profiles\meter\mods\"

echo Calling commit.bat...
call "C:\Users\<USER>\Desktop\desktop\prog\Java\Fabric\1.21.5\Mace\commit.bat" "%commitMessage%"

echo Script completed!
pause