name: Publish Development Build
on: push

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21

      - name: Build with Gradle
        run: ./gradlew build

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag_name: snapshot
          name: Dev Build
          prerelease: true
          files: |
            ./build/libs/*.jar
