@echo off
:: commit.bat - commit changes to git, skipping large files, using a passed commit message

:: Get commit message from first argument
if "%~1"=="" (
    set /p commit_msg=Enter commit message: 
) else (
    set "commit_msg=%~1"
)

echo Commit message: %commit_msg%

:: Remove files larger than 100MB from staging
for /r %%f in (*) do (
    if %%~zf GTR 104857600 (
        echo Skipping large file: %%f (%%~zf bytes)
        git rm --cached "%%f" >nul 2>&1
    )
)

:: Stage remaining files
git add -A

:: Commit with message
git commit -m "%commit_msg%"

:: Get current branch
for /f "tokens=*" %%b in ('git branch --show-current') do set branch=%%b

:: Push to origin
git push origin %branch%

pause
