package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.EnchantmentUtils;
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.FireworksComponent;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos;
import net.minecraft.block.BlockState;
import net.minecraft.util.math.MathHelper;
import java.util.function.Predicate;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class MaceAura extends Module {
   private enum AttackMode {
       Packet,
       KeyPress
   }

   private final SettingGroup sgGeneral = settings.getDefaultGroup();
   private final SettingGroup sgElytraSwap = settings.createGroup("Elytra Swap");

   private final Setting<Boolean> autoTakeOffElytra = sgGeneral.add(new BoolSetting.Builder()
       .name("auto-take-off-elytra").description("Automatically take off elytra when conditions are met.").defaultValue(false).build());

   private final Setting<AttackMode> attackMode = sgGeneral.add(new EnumSetting.Builder<AttackMode>()
       .name("attack-mode")
       .description("The method used to attack the target.")
       .defaultValue(AttackMode.Packet)
       .build()
   );
   private final Setting<Double> preSwapDistance = sgElytraSwap.add(new DoubleSetting.Builder()
    .name("pre-swap-distance")
    .description("Distance from ground to start holding chestplate.")
    .defaultValue(10.0)
    .min(3.0)
    .max(20.0)
    .sliderMin(3.0)
    .sliderMax(20.0)
    .visible(autoTakeOffElytra::get)
    .build()
);

private final Setting<Double> interactDistance = sgElytraSwap.add(new DoubleSetting.Builder()
    .name("interact-distance")
    .description("Distance from ground to interact with chestplate (equip it).")
    .defaultValue(3.0)
    .min(1.0)
    .max(10.0)
    .sliderMin(1.0)
    .sliderMax(10.0)
    .visible(autoTakeOffElytra::get)
    .build()
);
   private final Setting<Double> aimSpeed = sgGeneral.add(new DoubleSetting.Builder()
       .name("aim-speed").description("The speed at which the player aims at the target.").defaultValue(0.5d).range(0.1d, 1.0d).build());
   // Removed CPS setting since we're removing attack delays
   private final Setting<Double> aimRange = sgGeneral.add(new DoubleSetting.Builder()
       .name("aim-range").description("Maximum distance to start aiming at targets.").defaultValue(15.0d).range(3.0d, 30.0d).sliderRange(3.0d, 30.0d).build());
   private final Setting<Double> attackReach = sgGeneral.add(new DoubleSetting.Builder()
       .name("attack-reach").description("Maximum distance to attack targets.").defaultValue(4.5d).range(2.0d, 6.0d).sliderRange(2.0d, 6.0d).build());
   private final Setting<Double> fov = sgGeneral.add(new DoubleSetting.Builder() // New FOV setting
       .name("fov")
       .description("The field of view (in degrees) within which to target entities.")
       .defaultValue(90.0)
       .min(0.0)
       .max(360.0)
       .sliderMin(0.0)
       .sliderMax(360.0)
       .build());
   private final Setting<Boolean> attributeSwap = sgGeneral.add(new BoolSetting.Builder()
       .name("attribute-swap").description("Whether to swap between density and breach maces.").defaultValue(true).build());
   private final Setting<Double> minFallDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("min-fall-distance").description("Minimum fall distance to trigger density mace swap.").defaultValue(3.0).min(0.0).sliderMin(0.0).visible(attributeSwap::get).build());
   private final Setting<Boolean> autoDetectMaces = sgGeneral.add(new BoolSetting.Builder()
       .name("auto-detect-maces").description("Automatically detects mace slots based on enchantments.").defaultValue(true).visible(attributeSwap::get).build());
   private final Setting<Integer> densityMaceSlot = sgGeneral.add(new IntSetting.Builder()
       .name("density-mace-slot").description("The slot of the density enchanted mace.").defaultValue(1).range(1, 9).sliderRange(1, 9).visible(() -> attributeSwap.get() && !autoDetectMaces.get()).build());
   private final Setting<Integer> breachMaceSlot = sgGeneral.add(new IntSetting.Builder()
       .name("breach-mace-slot").description("The slot of the breach enchanted mace.").defaultValue(2).range(1, 9).sliderRange(1, 9).visible(() -> attributeSwap.get() && !autoDetectMaces.get()).build());
   private final Setting<Integer> swapDelay = sgGeneral.add(new IntSetting.Builder()
       .name("swap-delay").description("Delay before swapping weapons.").defaultValue(2).range(0, 20).sliderRange(0, 20).visible(attributeSwap::get).build());
   private final Setting<Integer> swapBackDelay = sgGeneral.add(new IntSetting.Builder()
       .name("swap-back-delay").description("Delay before swapping back to original weapon.").defaultValue(2).range(0, 20).sliderRange(0, 20).visible(attributeSwap::get).build());
   private final Setting<Integer> minCharge = sgGeneral.add(new IntSetting.Builder()
       .name("min-charge").description("Minimum charge percentage required for attribute swapping (0-100%).").defaultValue(25).range(0, 100).sliderRange(0, 100).visible(attributeSwap::get).build());

   private final Setting<Boolean> takeOffOnGround = sgGeneral.add(new BoolSetting.Builder()
       .name("take-off-on-ground").description("Take off elytra when enemy is on ground.").defaultValue(true).visible(autoTakeOffElytra::get).build());
   private final Setting<Boolean> takeOffNoFirework = sgGeneral.add(new BoolSetting.Builder()
       .name("take-off-no-firework").description("Take off elytra when enemy is not holding firework or wind charge.").defaultValue(true).visible(autoTakeOffElytra::get).build());
   private final Setting<Double> groundCheckDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("ground-check-distance").description("Distance from ground to trigger elytra swap and attack.").defaultValue(0.5).range(0.0, 5.0).sliderRange(0.0, 5.0).build());
   private final Setting<Double> elytraTakeOffDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("elytra-take-off-distance").description("Distance from attack range to take off elytra.").defaultValue(3.0).range(0.0, 10.0).sliderRange(0.0, 10.0).visible(autoTakeOffElytra::get).build());
   private final Setting<Boolean> waitLastMoment = sgGeneral.add(new BoolSetting.Builder()
       .name("wait-last-moment").description("Wait until the last possible moment to attack.").defaultValue(true).build());
   private final Setting<Integer> minAttackInterval = sgGeneral.add(new IntSetting.Builder()
       .name("min-attack-interval").description("Minimum time between attacks in milliseconds (prevents spam hitting).").defaultValue(100).range(50, 1000).sliderRange(50, 1000).build());
   private final Setting<Boolean> detailedLogging = sgGeneral.add(new BoolSetting.Builder()
       .name("detailed-logging").description("Enable detailed logging for debugging.").defaultValue(false).build());
   
   // --- New setting for keep target mode ---
   private final Setting<Boolean> keepTarget = sgGeneral.add(new BoolSetting.Builder()
       .name("keep-target")
       .description("Keep the same target until they die or move too far away.")
       .defaultValue(false)
       .build()
   );

   private final Setting<Double> keepTargetDistance = sgGeneral.add(new DoubleSetting.Builder()
       .name("keep-target-distance")
       .description("Maximum distance to keep the target.")
       .defaultValue(10.0)
       .min(5.0)
       .sliderMax(20.0)
       .visible(keepTarget::get)
       .build()
   );
   // --- End new setting ---

   // --- New setting for exponential smoothing ---
   private final Setting<Double> exponentialSmoothing = sgGeneral.add(new DoubleSetting.Builder()
       .name("exponential-smoothing")
       .description("Exponential smoothing factor for aiming (0.0 = no smoothing, 1.0 = maximum smoothing).")
       .defaultValue(0.0)
       .min(0.0)
       .max(1.0)
       .sliderMin(0.0)
       .sliderMax(1.0)
       .build()
   );
   // --- End new setting ---

   // --- Randomized aiming settings ---
   private final Setting<Boolean> enableRandomizedAiming = sgGeneral.add(new BoolSetting.Builder()
       .name("randomized-aiming")
       .description("Enable randomized aiming to make targeting appear more natural and less robotic.")
       .defaultValue(true)
       .build()
   );

   private final Setting<Double> randomizationStrength = sgGeneral.add(new DoubleSetting.Builder()
       .name("randomization-strength")
       .description("How much randomization to apply to aiming (0.0 = no randomization, 1.0 = full randomization).")
       .defaultValue(0.3)
       .min(0.0)
       .max(1.0)
       .sliderMin(0.0)
       .sliderMax(1.0)
       .visible(() -> enableRandomizedAiming.get())
       .build()
   );

   private final Setting<Double> proximityBias = sgGeneral.add(new DoubleSetting.Builder()
       .name("proximity-bias")
       .description("How much to prefer aiming points closer to current crosshair position (0.0 = no bias, 1.0 = strong bias).")
       .defaultValue(0.7)
       .min(0.0)
       .max(1.0)
       .sliderMin(0.0)
       .sliderMax(1.0)
       .visible(() -> enableRandomizedAiming.get())
       .build()
   );

   private final Setting<Boolean> preferVitalAreas = sgGeneral.add(new BoolSetting.Builder()
       .name("prefer-vital-areas")
       .description("Prefer targeting head and torso areas for more realistic aiming patterns.")
       .defaultValue(true)
       .visible(() -> enableRandomizedAiming.get())
       .build()
   );
   // --- End randomized aiming settings ---

   private LivingEntity target = null;
   private int originalSlot = -1;
   private int swapBackTimer = -1;
   private long lastClickTime = 0;
   private Targets targetsModule;
   private double distanceToTarget;

   // Declared delay variables as settings
   private final Setting<Integer> elytraSwapDelayTicks = sgElytraSwap.add(new IntSetting.Builder()
       .name("swap-delay-ticks").description("Delay (in ticks) before swapping to the target item for elytra swap.").defaultValue(1).min(0).build());
   private final Setting<Integer> elytraInteractDelayTicks = sgElytraSwap.add(new IntSetting.Builder()
       .name("interact-delay-ticks").description("Delay (in ticks) after swapping and before interacting (equipping) for elytra swap.").defaultValue(1).min(0).build());
   private final Setting<Integer> elytraSwapBackDelayTicks = sgElytraSwap.add(new IntSetting.Builder()
       .name("swap-back-delay-ticks").description("Delay (in ticks) after interacting and before swapping back to original slot for elytra swap.").defaultValue(1).min(0).build());

   // Variables for elytra swap state machine
   private int elytraSwapTimer = -1;
   private int elytraSwapState = 0; // 0=idle, 1=swapToTarget, 2=interact, 3=swapBack
   private FindItemResult targetElytraItem;
   private int originalElytraHotbarSlot = -1;
   private boolean elytraSwapInProgress = false;

   private boolean preSwapActive = false;
   private int preSwapSlot = -1;
   private int originalPreSwapSlot = -1;

   private Method doAttackMethod = null;

   // Add a static field to track which module performed an action this tick
   private static boolean actionTakenThisTick = false;

   // Track last attack time to prevent spam hitting
   private long lastAttackTime = 0;
   
   private static class PhysicsResult {
       double ticksToGround;
       double ticksToOutOfRange; // This line still has `double double`
       
       PhysicsResult(double ticksToGround, double ticksToOutOfRange) {
           this.ticksToGround = ticksToGround;
           this.ticksToOutOfRange = ticksToOutOfRange;
       }
   }

   public MaceAura() {
       super(AddonTemplate.CATEGORY, "mace-aura", "Automatically aims and attacks with maces.");
       targetsModule = Modules.get().get(Targets.class);

       // Initialize the reflection method - this is a critical part
       initializeAttackMethod();
   }

   @Override
   public void onActivate() {
       super.onActivate();
   }

   private void initializeAttackMethod() {
       try {
           // Try different method names - 1.21.5 may use a different obfuscated name
           try {
               doAttackMethod = MinecraftClient.class.getDeclaredMethod("method_1536");
           } catch (NoSuchMethodException e1) {
               try {
                   // Try the actual method name (in development environment)
                   doAttackMethod = MinecraftClient.class.getDeclaredMethod("doAttack");
               } catch (NoSuchMethodException e2) {
                   // If all attempts fail
                   error("Failed to find Minecraft's attack method via reflection. " +
                       "Module will use alternative attack method.");
                   return;
               }
           }
           
           // Make the method accessible
           doAttackMethod.setAccessible(true);
           info("Successfully found attack method via reflection.");
       } catch (Exception e) {
           error("Failed to initialize attack method: " + e.getMessage());
           e.printStackTrace();
       }
   }

   private void log(String message) {
       if (detailedLogging.get()) {
           info(message);
       }
   }

   // Removed getClickDelayMillis method since we're removing attack delays

   private int getFireworkDuration(ItemStack fireworkStack) {
       if (fireworkStack == null || fireworkStack.getItem() != Items.FIREWORK_ROCKET) {
           return 0;
       }
       FireworksComponent fireworksComponent = fireworkStack.get(DataComponentTypes.FIREWORKS);
       if (fireworksComponent != null) {
           return fireworksComponent.flightDuration();
       }
       return 0;
   }

   private void findMaces() {
       if (mc.player == null) return;
       int foundDensityMaceSlot = -1;
       int foundBreachMaceSlot = -1;
       for (int i = 0; i < 9; i++) {
           ItemStack stack = mc.player.getInventory().getStack(i);
           if (stack.getItem() == Items.MACE) {
               if (EnchantmentUtils.hasEnchantment(stack, Enchantments.DENSITY)) {
                   foundDensityMaceSlot = i;
               } else if (EnchantmentUtils.hasEnchantment(stack, Enchantments.BREACH)) {
                   foundBreachMaceSlot = i;
               }
           }
       }
       if (foundDensityMaceSlot != -1) {
           densityMaceSlot.set(foundDensityMaceSlot + 1);
       }
       if (foundBreachMaceSlot != -1) {
           breachMaceSlot.set(foundBreachMaceSlot + 1);
       }
   }

   // Modified shouldTakeOffElytra method - this was the main issue
private boolean shouldTakeOffElytra() {
    if (!autoTakeOffElytra.get() || target == null || elytraSwapInProgress) return false;
    ItemStack equippedChest = mc.player.getInventory().getStack(38);
    if (equippedChest.getItem() != Items.ELYTRA) return false;
    
    double distanceToGround = mc.player.getPos().y - findGroundLevel(mc.player.getPos());
    this.distanceToTarget = mc.player.distanceTo(target);
    
    // Key fix: Always check interaction distance regardless of pre-swap status
    if (distanceToGround <= interactDistance.get() && distanceToTarget <= attackReach.get() + 5.0) {
        boolean conditionMet = true;
        if (takeOffOnGround.get() && !target.isOnGround()) conditionMet = false;
        if (takeOffNoFirework.get()) {
            ItemStack mainHand = target.getMainHandStack();
            ItemStack offHand = target.getOffHandStack();
            int fireworkDuration = 0;
            if (mainHand.getItem() == Items.FIREWORK_ROCKET) fireworkDuration = getFireworkDuration(mainHand);
            else if (offHand.getItem() == Items.FIREWORK_ROCKET) fireworkDuration = getFireworkDuration(offHand);
            boolean hasWindCharge = mainHand.getItem() == Items.WIND_CHARGE || offHand.getItem() == Items.WIND_CHARGE;
            if (fireworkDuration > 0 || hasWindCharge) conditionMet = false;
        }
        return conditionMet;
    }
    
    return false;
}
   // Replace the existing handleElytraPreSwap method with this improved version:
// Also modify handleElytraPreSwap to NOT call startElytraSwap (remove this conflict)
private void handleElytraPreSwap() {
    if (!autoTakeOffElytra.get() || target == null) {
        if (preSwapActive) {
            // Reset pre-swap if conditions are no longer met
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon - pre-swap cancelled.");
            }
            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;
            log("Deactivating pre-swap: target lost or module disabled.");
        }
        return;
    }

    ItemStack equippedChest = mc.player.getInventory().getStack(38);
    if (equippedChest.getItem() != Items.ELYTRA) {
        // If elytra is not equipped, reset pre-swap
        if (preSwapActive) {
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon - elytra not equipped.");
            }
            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;
            log("Deactivating pre-swap: elytra not equipped.");
        }
        return;
    }

    // Safely get the targets module with null check
    Targets targetsModule = Modules.get().get(Targets.class);
    if (targetsModule == null || !targetsModule.isActive()) {
        // If targets module is not available or not active, reset pre-swap
        if (preSwapActive) {
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon - targets module not available.");
            }
            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;
            log("Deactivating pre-swap: targets module not available.");
        }
        return;
    }

    double distanceToTarget = mc.player.distanceTo(target);
    double distanceToGround = mc.player.getPos().y - findGroundLevel(mc.player.getPos());
    
    // Check if we're within reasonable distance to target and above the pre-swap distance
    boolean shouldPreSwap = distanceToTarget <= targetsModule.range.get() && 
                           distanceToGround >= preSwapDistance.get() && 
                           mc.player.fallDistance > 1.0 && // Make sure we're actually falling
                           !elytraSwapInProgress;
    
    if (shouldPreSwap && !preSwapActive) {
        // Find chestplate in hotbar
        FindItemResult chestplateItem = InvUtils.findInHotbar(itemStack -> 
            itemStack.getItem() == Items.DIAMOND_CHESTPLATE || 
            itemStack.getItem() == Items.NETHERITE_CHESTPLATE || 
            itemStack.getItem() == Items.IRON_CHESTPLATE || 
            itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE || 
            itemStack.getItem() == Items.GOLDEN_CHESTPLATE || 
            itemStack.getItem() == Items.LEATHER_CHESTPLATE
        );
        
        if (chestplateItem.found()) {
            // Store current slot before swapping to chestplate
            FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
            originalPreSwapSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;
            
            // Swap to chestplate and hold it
            InvUtils.swap(chestplateItem.slot(), false);
            
            preSwapActive = true;
            preSwapSlot = chestplateItem.slot();
            log(String.format("Pre-swapped to chestplate at %.1f blocks above ground.", distanceToGround));
        } else {
            log("No chestplate found in hotbar for pre-swap.");
        }
    } else if (preSwapActive && distanceToTarget > targetsModule.range.get()) {
        // Only cancel pre-swap if target is too far, let shouldTakeOffElytra handle the interaction timing
        if (originalPreSwapSlot != -1) {
            InvUtils.swap(originalPreSwapSlot, false);
            log("Swapped back to original weapon - target too far.");
        }
        preSwapActive = false;
        preSwapSlot = -1;
        originalPreSwapSlot = -1;
        log("Deactivating pre-swap: target too far.");
    }
    // Removed the interaction logic from here - let the main flow handle it
}

   private double calculateTimeToImpact() {
       if (target == null) return Double.MAX_VALUE;
       
       Vec3d playerPos = mc.player.getPos();
       Vec3d targetPos = target.getPos();
       Vec3d velocity = mc.player.getVelocity();
       
       double distanceAlongPath = playerPos.distanceTo(targetPos);
       double currentSpeed = velocity.length();
       
       int predictedFireworkDuration = 0;
       if (mc.player.getMainHandStack().getItem() == Items.FIREWORK_ROCKET) {
           predictedFireworkDuration = getFireworkDuration(mc.player.getMainHandStack());
       } else if (mc.player.getOffHandStack().getItem() == Items.FIREWORK_ROCKET) {
           predictedFireworkDuration = getFireworkDuration(mc.player.getOffHandStack());
       }
       
       if (predictedFireworkDuration > 0) {
           double fireworkBoostTicks = predictedFireworkDuration * 20.0;
           double estimatedBoostDistance = fireworkBoostTicks * 0.5;
           distanceAlongPath -= estimatedBoostDistance;
       }
       
       double estimatedSpeed = Math.max(currentSpeed, 1.0);
       return distanceAlongPath / estimatedSpeed;
   }

   private void startElytraSwap() {
    if (mc.player == null) return;

    ItemStack equippedChest = mc.player.getInventory().getStack(38);

    if (preSwapActive && equippedChest.getItem() == Items.ELYTRA) {
        // Chestplate is already in hand, start the interaction sequence
        elytraSwapState = 5; // New state for pre-swapped interaction
        elytraSwapTimer = elytraInteractDelayTicks.get();
        elytraSwapInProgress = true;
        
        log("Starting pre-swapped elytra removal sequence.");
        return;
    }

    // Regular swap logic for when no pre-swap occurred
    FindItemResult targetItemToFind = null;
    boolean isEquippedChestplate = equippedChest.getItem() == Items.DIAMOND_CHESTPLATE ||
                                   equippedChest.getItem() == Items.NETHERITE_CHESTPLATE ||
                                   equippedChest.getItem() == Items.IRON_CHESTPLATE ||
                                   equippedChest.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                                   equippedChest.getItem() == Items.GOLDEN_CHESTPLATE ||
                                   equippedChest.getItem() == Items.LEATHER_CHESTPLATE;

    if (equippedChest.getItem() == Items.ELYTRA) {
        targetItemToFind = InvUtils.findInHotbar(itemStack ->
            itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
            itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
            itemStack.getItem() == Items.IRON_CHESTPLATE ||
            itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
            itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
            itemStack.getItem() == Items.LEATHER_CHESTPLATE
        );
    } else if (isEquippedChestplate) {
        targetItemToFind = InvUtils.findInHotbar(Items.ELYTRA);
    } else {
        targetItemToFind = InvUtils.findInHotbar(Items.ELYTRA);
        if (!targetItemToFind.found()) {
            targetItemToFind = InvUtils.findInHotbar(itemStack ->
                itemStack.getItem() == Items.DIAMOND_CHESTPLATE ||
                itemStack.getItem() == Items.NETHERITE_CHESTPLATE ||
                itemStack.getItem() == Items.IRON_CHESTPLATE ||
                itemStack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
                itemStack.getItem() == Items.GOLDEN_CHESTPLATE ||
                itemStack.getItem() == Items.LEATHER_CHESTPLATE
            );
        }
    }

    if (targetItemToFind != null && targetItemToFind.found()) {
        this.targetElytraItem = targetItemToFind;
        FindItemResult originalSlotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        originalElytraHotbarSlot = originalSlotResult.found() ? originalSlotResult.slot() : -1;

        elytraSwapState = 1;
        elytraSwapTimer = elytraSwapDelayTicks.get();
        elytraSwapInProgress = true;
        log("Starting regular elytra/chestplate swap process.");
    } else {
        log("No suitable item (elytra or chestplate) found in hotbar to initiate swap.");
    }
}

// Update the handleElytraSwap method to handle the new state 4:
private void handleElytraSwap() {
    if (!elytraSwapInProgress) return;

    if (elytraSwapTimer > 0) {
        elytraSwapTimer--;
    } else {
        if (elytraSwapState == 1) { // State 1: Swap to target item
            if (targetElytraItem != null && targetElytraItem.found()) {
                InvUtils.swap(targetElytraItem.slot(), false);
                elytraSwapState = 2;
                elytraSwapTimer = elytraInteractDelayTicks.get();
            } else {
                log("Error: Target item not found for swap state 1. Aborting swap.");
                elytraSwapInProgress = false;
            }
        } else if (elytraSwapState == 2) { // State 2: Interact (equip)
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            log("Interacted with item (equipped).");
            elytraSwapState = 3;
            elytraSwapTimer = elytraSwapBackDelayTicks.get();
        } else if (elytraSwapState == 3) { // State 3: Swap back to original slot
            if (originalElytraHotbarSlot != -1) {
                InvUtils.swap(originalElytraHotbarSlot, false);
                log("Swapped back to original slot.");
            } else {
                log("Warning: Original hotbar slot was -1. Could not swap back to original item.");
            }
            // Reset all state variables
            elytraSwapState = 0;
            elytraSwapTimer = -1;
            originalElytraHotbarSlot = -1;
            targetElytraItem = null;
            elytraSwapInProgress = false;
            log("Elytra/chestplate swap complete.");
        } else if (elytraSwapState == 4) { // State 4: Swap back after instant equip (old logic)
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to original weapon after instant equip.");
            }
            // Reset all state variables
            elytraSwapState = 0;
            elytraSwapTimer = -1;
            originalPreSwapSlot = -1;
            elytraSwapInProgress = false;
            log("Instant elytra swap complete.");
        } else if (elytraSwapState == 5) { // State 5: Pre-swapped interaction sequence
            // Interact with chestplate (equip it)
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            log("Equipped chestplate from pre-swap.");
            elytraSwapState = 6; // Move to sword swap state
            elytraSwapTimer = elytraSwapBackDelayTicks.get();
        } else if (elytraSwapState == 6) { // State 6: Swap back to sword after pre-swap interaction
            if (originalPreSwapSlot != -1) {
                InvUtils.swap(originalPreSwapSlot, false);
                log("Swapped back to sword after chestplate equip.");
            }
            
            // Reset pre-swap variables
            preSwapActive = false;
            preSwapSlot = -1;
            originalPreSwapSlot = -1;
            
            // Reset swap state variables  
            elytraSwapState = 0;
            elytraSwapTimer = -1;
            elytraSwapInProgress = false;
            
            log("Pre-swap sequence complete. Ready for attribute swap.");
        }
    }
}
   private PhysicsResult calculateCollisionTiming() {
       if (target == null) return new PhysicsResult(Double.MAX_VALUE, Double.MAX_VALUE);
       
       Vec3d playerPos = mc.player.getPos();
       Vec3d playerVel = mc.player.getVelocity();
       Vec3d targetPos = target.getPos();
       Vec3d targetVel = target.getVelocity();
       
       double ticksToGround = calculateTicksToGround(playerPos, playerVel);
       double ticksToOutOfRange = calculateTicksToOutOfRange(playerPos, playerVel, targetPos, targetVel);
       
       return new PhysicsResult(ticksToGround, ticksToOutOfRange);
   }

   private double calculateTicksToGround(Vec3d pos, Vec3d velocity) {
       double gravity = 0.08;
       double drag = 0.98;
       
       double y = pos.y;
       double vy = velocity.y;
       double groundY = findGroundLevel(pos);
       
       if (y <= groundY) return 0;
       
       for (int tick = 0; tick < 200; tick++) {
           y += vy;
           vy = (vy - gravity) * drag;
           
           if (y <= groundY + groundCheckDistance.get()) {
               return tick + 1;
           }
       }
       
       return Double.MAX_VALUE;
   }

   private double calculateTicksToOutOfRange(Vec3d playerPos, Vec3d playerVel, Vec3d targetPos, Vec3d targetVel) {
       double gravity = 0.08;
       double drag = 0.98;
       
       Vec3d pPos = new Vec3d(playerPos.x, playerPos.y, playerPos.z);
       Vec3d pVel = new Vec3d(playerVel.x, playerVel.y, playerVel.z);
       Vec3d tPos = new Vec3d(targetPos.x, targetPos.y, targetPos.z);
       Vec3d tVel = new Vec3d(targetVel.x, targetVel.y, targetVel.z);
       
       for (int tick = 0; tick < 100; tick++) {
           pPos = pPos.add(pVel);
           pVel = new Vec3d(pVel.x * drag, (pVel.y - gravity) * drag, pVel.z * drag);
           
           tPos = tPos.add(tVel);
           if (!target.isOnGround()) { // Only apply gravity/drag to target if they are airborne
               tVel = new Vec3d(tVel.x * drag, (tVel.y - gravity) * drag, tVel.z * drag);
           }
           
           double distance = pPos.distanceTo(tPos);
           if (distance > attackReach.get()) {
               return tick + 1;
           }
       }
       
       return Double.MAX_VALUE;
   }

   private double findGroundLevel(Vec3d pos) {
       for (int y = (int)pos.y; y >= mc.world.getBottomY(); y--) {
           BlockPos blockPos = new BlockPos((int)pos.x, y, (int)pos.z);
           BlockState blockState = mc.world.getBlockState(blockPos);
           
           // Check if the block is a solid block that has collision
           if (!blockState.getCollisionShape(mc.world, blockPos).isEmpty()) {
               return y + 1; // Return the block above the solid block
           }
       }
       return mc.world.getBottomY(); // Fallback to world bottom if no ground found
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
       if (mc.player == null) return;

       // Check if the module is active
       if (!isActive()) {
           return;
       }

       // Check if another module has already taken action this tick
       if (actionTakenThisTick) {
           log("Another module has already taken action this tick. Skipping MaceAura.");
           return;
       }

       if (autoDetectMaces.get()) findMaces();
       handleElytraSwap(); // This now uses the state machine and delays
       handleElytraPreSwap(); // This is for preemptive equipping
       
       // Existing weapon swap logic
       if (swapBackTimer > 0) {
           swapBackTimer--;
       } else if (swapBackTimer == 0 && originalSlot != -1) {
           InvUtils.swap(originalSlot, true); // true for hotbar slot
           swapBackTimer = -1;
           originalSlot = -1;
       }

       LivingEntity oldTarget = this.target;

       Targets targetsModule = Modules.get().get(Targets.class);
       if (targetsModule == null || !targetsModule.isActive()) {
           this.target = null;
           if (oldTarget != null) info("Lost target (Targets module not active).");
           return;
       }

       // If keep target mode is enabled and we have a valid current target, check if we should keep it
       if (keepTarget.get() && this.target != null) {
           // Check if target is still alive and within distance
           if (this.target.isAlive() && mc.player.distanceTo(this.target) <= keepTargetDistance.get()) {
               // Check if target is still within FOV if FOV is not 360
               if (fov.get() >= 360.0 || isWithinFOV(this.target)) {
                   // Keep the same target
               } else {
                   // Target is out of FOV, need to find a new target
                   this.target = null;
               }
           } else {
               // Target is dead or too far away, need to find a new target
               this.target = null;
           }
       }

       // Only find a new target if we don't have one or keepTarget is disabled
       if (this.target == null) {
           Predicate<Entity> targetPredicate = entity -> {
               if (entity.equals(mc.player)) return false;
               
               // Use the targetsModule field instead of redeclaring it
               if (targetsModule == null || !targetsModule.isActive()) {
                   return false;
               }
               
               // Use the new shouldTarget method to check if we should target this entity
               return targetsModule.shouldTarget(entity);
           };

           Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

           if (foundTarget instanceof LivingEntity) {
               LivingEntity livingTarget = (LivingEntity) foundTarget;
               // Check if target is within FOV
               if (fov.get() >= 360.0 || isWithinFOV(livingTarget)) {
                   this.target = livingTarget;
               } else {
                   this.target = null;
               }
           } else {
               this.target = null;
           }
       }

       if (this.target != null && this.target != oldTarget) {
           info("New target acquired: %s (%.2f blocks away)", this.target.getName().getString(), mc.player.distanceTo(this.target));
       } else if (this.target == null && oldTarget != null) {
           info("Lost target.");
       }

       if (this.target == null) return;

       // Update distance to target
       distanceToTarget = mc.player.distanceTo(this.target);

       // Trigger elytra swap based on conditions (now calls the startElytraSwap state machine)
       if (shouldTakeOffElytra() && !elytraSwapInProgress) { // Only start if not already in progress
           log("Conditions met to take off elytra. Starting swap...");
           startElytraSwap();
       }

       // Removed CPS timing check to remove attack delays
       boolean inRange = distanceToTarget <= attackReach.get();
       boolean fallDistanceMet = !attributeSwap.get() || mc.player.fallDistance >= minFallDistance.get();
       boolean isFalling = !mc.player.isOnGround();
       boolean isSwappingElytra = elytraSwapInProgress; // Use the module's internal state
       
       // When minCharge is 0, we still respect attack cooldown to prevent spam
       boolean minChargeMet = !attributeSwap.get() || mc.player.getAttackCooldownProgress(0.0f) >= (minCharge.get() / 100.0f);
       boolean fullyCharged = mc.player.getAttackCooldownProgress(0.0f) >= 1.0f;

       // Key change: If not in attack range, don't proceed with attack calculations
       if (!inRange) {
           if (detailedLogging.get()) {
               log(String.format("Not attacking %s. Target not in attack reach (%.2f > %.2f).", 
                   target.getName().getString(), distanceToTarget, attackReach.get()));
           }
           return;
       }

       boolean mustAttackNow;
       String reason = "";

       if (waitLastMoment.get()) {
           PhysicsResult physics = calculateCollisionTiming();
           boolean aboutToHitGround = physics.ticksToGround <= 2;
           boolean targetLeavingRange = physics.ticksToOutOfRange <= 2;
           mustAttackNow = aboutToHitGround || targetLeavingRange || fullyCharged;

           reason = fullyCharged ? "Fully charged" :
                   aboutToHitGround ? String.format("About to hit ground (%.1f ticks)", physics.ticksToGround) :
                   String.format("Target leaving range (%.1f ticks)", physics.ticksToOutOfRange);
       } else {
           // Only attack when fully charged or when minCharge requirement is met
           mustAttackNow = fullyCharged || (minCharge.get() == 0 && minChargeMet);
           reason = "Attack conditions met";
       }

       // Removed CPS timing check - attack as fast as possible
       if (!fallDistanceMet || !isFalling || isSwappingElytra || !minChargeMet) {
           if (detailedLogging.get()) {
               StringBuilder debugReason = new StringBuilder(String.format("Not attacking %s. Reasons: ", target.getName().getString()));
               if (!fallDistanceMet) debugReason.append("Fall distance too low. ");
               if (!isFalling) debugReason.append("Player is on the ground. ");
               if (isSwappingElytra) debugReason.append("Elytra swap in progress. ");
               if (!minChargeMet) debugReason.append("Min charge not met. ");
               log(debugReason.toString());
           }
           return;
       }

       if (!mustAttackNow) {
           return;
       }

       // Check attack interval to prevent spam hitting
       long currentTime = System.currentTimeMillis();
       if (currentTime - lastAttackTime < minAttackInterval.get()) {
           if (detailedLogging.get()) {
               log("Attack interval not met. Time since last attack: " + (currentTime - lastAttackTime) + "ms (min: " + minAttackInterval.get() + "ms)");
           }
           return;
       }

       info("Executing attack on %s with %s mode. Reason: %s", target.getName().getString(), attackMode.get().name(), reason);
       
       // Mark that this module is taking action this tick
       actionTakenThisTick = true;

       // Update last attack time to prevent spam hitting
       lastAttackTime = currentTime;
       
       if (attributeSwap.get() && originalSlot == -1) {
           FindItemResult findItemResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
           if (findItemResult.found()) {
               originalSlot = findItemResult.slot();
               if (mc.player.fallDistance >= minFallDistance.get()) {
                   log("High fall distance. Swapping to Density mace.");
                   InvUtils.swap(densityMaceSlot.get() - 1, false);
               } else {
                   log("Low fall distance. Swapping to Breach mace.");
                   InvUtils.swap(breachMaceSlot.get() - 1, false);
               }
               swapBackTimer = Math.max(swapBackDelay.get(), 1); // Set the return swap delay
           }
       }

       attackTarget();
       info("Attack executed successfully. Next attack available in " + minAttackInterval.get() + "ms");
   }

   private void attackTarget() {
       if (attackMode.get() == AttackMode.KeyPress) {
           if (doAttackMethod == null) {
               // Re-initialize if it's null - may have been reset
               initializeAttackMethod();
               
               // If still null after re-initialization, use alternative method
               if (doAttackMethod == null) {
                   error("KeyPress mode enabled, but 'doAttack' method object is null. Falling back to Packet mode.");
                   // Fall back to packet mode or direct interaction
                   if (mc.interactionManager != null && target != null) {
                       mc.interactionManager.attackEntity(mc.player, target);
                       mc.player.swingHand(Hand.MAIN_HAND);
                   }
                   return;
               }
           }
           
           try {
               doAttackMethod.invoke(mc);
           } catch (Exception e) {
               error("Failed to invoke attack method: " + e.getMessage());
               // Fall back to packet mode
               if (mc.interactionManager != null && target != null) {
                   mc.interactionManager.attackEntity(mc.player, target);
                   mc.player.swingHand(Hand.MAIN_HAND);
               }
           }
       } else {
           // Packet mode
           if (mc.interactionManager != null && target != null) {
               mc.interactionManager.attackEntity(mc.player, target);
               mc.player.swingHand(Hand.MAIN_HAND);
           }
       }
   }

   @EventHandler
   private void onRender3d(Render3DEvent event) {
       if (mc.player == null || target == null || mc.player.distanceTo(target) > aimRange.get() ||
           (attributeSwap.get() && (mc.player.fallDistance < minFallDistance.get() || (minCharge.get() > 0 && mc.player.getAttackCooldownProgress(0.0f) < (minCharge.get() / 100.0f)))) ||
           (fov.get() < 360.0 && !isWithinFOV(target)) ||
           isPlayerFlyingWithElytra()) { // Don't aim when flying with elytra
           return;
       }

       Vec3d playerPos = mc.player.getEyePos();
       // Use eye position for more accurate targeting, consistent with Aimbot
       Vec3d targetPos = target.getEyePos();

       // Use current player angles as starting point for smooth aiming
       float currentYaw = mc.player.getYaw();
       float currentPitch = mc.player.getPitch();

       // Use smooth aiming utility for better aiming
       float[] rotations;

       if (enableRandomizedAiming.get()) {
           // Use randomized aiming for more natural targeting
           if (exponentialSmoothing.get() > 0.0) {
               // Use exponential smoothing with randomization
               rotations = SmoothAimingUtils.calculateRandomizedExponentialSmoothRotations(
                   currentYaw,
                   currentPitch,
                   target,
                   playerPos,
                   aimSpeed.get(),
                   (float) event.frameTime,
                   exponentialSmoothing.get(),
                   randomizationStrength.get(),
                   proximityBias.get()
               );
           } else {
               // Use smart randomized aiming with body part targeting
               rotations = SmoothAimingUtils.calculateSmartRandomizedSmoothRotations(
                   currentYaw,
                   currentPitch,
                   target,
                   playerPos,
                   aimSpeed.get(),
                   (float) event.frameTime,
                   randomizationStrength.get(),
                   proximityBias.get(),
                   preferVitalAreas.get()
               );
           }
       } else {
           // Use traditional aiming without randomization
           if (exponentialSmoothing.get() > 0.0) {
               // Use exponential smoothing for even smoother aiming
               rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                   currentYaw,
                   currentPitch,
                   targetPos,
                   playerPos,
                   aimSpeed.get(),
                   (float) event.frameTime,
                   exponentialSmoothing.get()
               );
           } else {
               // Use regular smoothing
               rotations = SmoothAimingUtils.calculateSmoothRotations(
                   currentYaw,
                   currentPitch,
                   targetPos,
                   playerPos,
                   aimSpeed.get(),
                   (float) event.frameTime
               );
           }
       }

       mc.player.setYaw(rotations[0]);
       mc.player.setPitch(rotations[1]);
   }
   
   // Removed duplicate attemptAttack() method - was causing potential spam hitting
   
   // New method to check if an entity is within the FOV
   private boolean isWithinFOV(LivingEntity entity) {
       if (mc.player == null || entity == null) return false;
       
       Vec3d playerPos = mc.player.getEyePos();
       Vec3d targetPos = entity.getEyePos();
       
       double deltaX = targetPos.x - playerPos.x;
       double deltaY = targetPos.y - playerPos.y;
       double deltaZ = targetPos.z - playerPos.z;
       
       double distanceXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
       
       // Calculate yaw and pitch to target
       float targetYaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0F;
       float targetPitch = (float) Math.toDegrees(-Math.atan2(deltaY, distanceXZ));
       
       // Clamp pitch to valid range
       targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

       // Get current player angles
       float currentYaw = mc.player.getYaw();
       float currentPitch = mc.player.getPitch();

       // Calculate shortest angle differences (no normalization to avoid jumps)
       float yawDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(currentYaw, targetYaw));
       float pitchDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(currentPitch, targetPitch));
       
       // Check if within FOV
       return yawDiff <= fov.get() / 2.0 && pitchDiff <= fov.get() / 2.0;
   }
   
   // Add a method to reset the action taken flag at the end of each tick
   public static void resetActionTaken() {
       actionTakenThisTick = false;
   }

   private boolean isPlayerFlyingWithElytra() {
       if (mc.player == null) return false;
       // Check if player has elytra equipped and is not on ground with upward or stable velocity
       ItemStack chestSlot = mc.player.getInventory().getStack(38); // Chest armor slot
       return chestSlot.getItem() == Items.ELYTRA &&
              !mc.player.isOnGround() &&
              mc.player.getVelocity().y > -0.5; // Not falling too fast (gliding)
   }
}