package com.example.addon.test;

import com.example.addon.utils.RandomizedAimingUtils;
import net.minecraft.util.math.Vec3d;

/**
 * Simple test class to verify randomized aiming functionality.
 * This can be used to manually test the randomization algorithms.
 */
public class RandomizedAimingTest {
    
    /**
     * Test the basic randomization functionality.
     */
    public static void testBasicRandomization() {
        System.out.println("=== Basic Randomization Test ===");
        
        // Test rotation randomization
        float[] originalRotations = {45.0f, -10.0f}; // yaw, pitch
        
        System.out.println("Original rotations: yaw=" + originalRotations[0] + ", pitch=" + originalRotations[1]);
        
        // Test with different randomization amounts
        double[] randomizationAmounts = {0.0, 0.5, 1.0, 2.0};
        
        for (double amount : randomizationAmounts) {
            float[] randomizedRotations = RandomizedAimingUtils.addRandomizationToRotation(
                originalRotations[0], originalRotations[1], amount
            );
            
            float yawDiff = Math.abs(randomizedRotations[0] - originalRotations[0]);
            float pitchDiff = Math.abs(randomizedRotations[1] - originalRotations[1]);
            
            System.out.println("Randomization amount: " + amount);
            System.out.println("  Randomized: yaw=" + String.format("%.2f", randomizedRotations[0]) + 
                             ", pitch=" + String.format("%.2f", randomizedRotations[1]));
            System.out.println("  Differences: yaw=" + String.format("%.2f", yawDiff) + 
                             ", pitch=" + String.format("%.2f", pitchDiff));
            System.out.println();
        }
        
        System.out.println("=== Basic Randomization Test Complete ===");
    }
    
    /**
     * Test the circular random offset functionality.
     */
    public static void testCircularRandomOffset() {
        System.out.println("=== Circular Random Offset Test ===");
        
        double[] radii = {0.5, 1.0, 2.0};
        
        for (double radius : radii) {
            System.out.println("Testing radius: " + radius);
            
            // Generate multiple offsets to show distribution
            for (int i = 0; i < 5; i++) {
                Vec3d offset = RandomizedAimingUtils.getCircularRandomOffset(radius);
                double actualRadius = Math.sqrt(offset.x * offset.x + offset.y * offset.y);
                
                System.out.println("  Offset " + (i+1) + ": x=" + String.format("%.3f", offset.x) + 
                                 ", y=" + String.format("%.3f", offset.y) + 
                                 ", actual radius=" + String.format("%.3f", actualRadius));
            }
            System.out.println();
        }
        
        System.out.println("=== Circular Random Offset Test Complete ===");
    }
    
    /**
     * Test randomization consistency (same input should produce different outputs).
     */
    public static void testRandomizationConsistency() {
        System.out.println("=== Randomization Consistency Test ===");
        
        float originalYaw = 90.0f;
        float originalPitch = 0.0f;
        double randomizationAmount = 1.0;
        
        System.out.println("Original: yaw=" + originalYaw + ", pitch=" + originalPitch);
        System.out.println("Randomization amount: " + randomizationAmount);
        System.out.println("Multiple randomizations of the same input:");
        
        boolean allSame = true;
        float[] firstResult = null;
        
        for (int i = 0; i < 10; i++) {
            float[] result = RandomizedAimingUtils.addRandomizationToRotation(
                originalYaw, originalPitch, randomizationAmount
            );
            
            if (firstResult == null) {
                firstResult = result.clone();
            } else {
                if (Math.abs(result[0] - firstResult[0]) > 0.001f || 
                    Math.abs(result[1] - firstResult[1]) > 0.001f) {
                    allSame = false;
                }
            }
            
            System.out.println("  Result " + (i+1) + ": yaw=" + String.format("%.3f", result[0]) + 
                             ", pitch=" + String.format("%.3f", result[1]));
        }
        
        System.out.println("All results identical: " + allSame + " (should be false for proper randomization)");
        
        System.out.println("\n=== Randomization Consistency Test Complete ===");
    }
    
    /**
     * Run all tests.
     */
    public static void runAllTests() {
        testBasicRandomization();
        System.out.println();
        testCircularRandomOffset();
        System.out.println();
        testRandomizationConsistency();
    }
}
