package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.NoiseUtils; // Added import for NoiseUtils
import com.example.addon.utils.RandomizedAimingUtils; // Added import for RandomizedAimingUtils
import com.example.addon.utils.RaycastUtils; // Added import for RaycastUtils
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos;
import net.minecraft.block.Blocks;
import java.util.function.Predicate;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class Aimbot extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("The maximum range to aim at entities.")
        .defaultValue(4.5)
        .min(0.0)
        .sliderMax(10.0)
        .build()
    );

    private final Setting<Double> fov = sgGeneral.add(new DoubleSetting.Builder() // New FOV setting
        .name("fov")
        .description("The field of view (in degrees) within which to target entities.")
        .defaultValue(90.0)
        .min(0.0)
        .max(360.0)
        .sliderMin(0.0)
        .sliderMax(360.0)
        .build()
    );

    private final Setting<Double> aimSpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("The speed at which the player aims at the target.")
        .defaultValue(0.5d)
        .range(0.1d, 1.0d)
        .build()
    );

    // --- New setting for block obstruction prevention ---
    private final Setting<Boolean> blockObstructedAttacks = sgGeneral.add(new BoolSetting.Builder()
        .name("block-obstructed-attacks")
        .description("Block all left-click attacks (including manual) on entities when obstructed by blocks.")
        .defaultValue(true)
        .build()
    );
    // --- End new setting ---

    // --- New setting for keep target mode ---
    private final Setting<Boolean> keepTarget = sgGeneral.add(new BoolSetting.Builder()
        .name("keep-target")
        .description("Keep the same target until they die or move too far away.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> keepTargetDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("keep-target-distance")
        .description("Maximum distance to keep the target.")
        .defaultValue(10.0)
        .min(5.0)
        .sliderMax(20.0)
        .visible(keepTarget::get)
        .build()
    );
    // --- End new setting ---

    private final Setting<Boolean> onlyWhileLMBHeld = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-LMB-held")
        .description("Only active while Left Mouse Button is held.")
        .defaultValue(true)
        .build()
    );

    // --- Randomized aiming settings ---
    private final Setting<Boolean> enableRandomizedAiming = sgGeneral.add(new BoolSetting.Builder()
        .name("randomized-aiming")
        .description("Enable randomized aiming to make targeting appear more natural and less robotic.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> randomizationStrength = sgGeneral.add(new DoubleSetting.Builder()
        .name("randomization-strength")
        .description("How much randomization to apply to aiming (0.0 = no randomization, 1.0 = full randomization).")
        .defaultValue(0.2)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .visible(() -> enableRandomizedAiming.get())
        .build()
    );

    private final Setting<Double> proximityBias = sgGeneral.add(new DoubleSetting.Builder()
        .name("proximity-bias")
        .description("How much to prefer aiming points closer to current crosshair position (0.0 = no bias, 1.0 = strong bias).")
        .defaultValue(0.8)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .visible(() -> enableRandomizedAiming.get())
        .build()
    );

    private final Setting<Boolean> preferVitalAreas = sgGeneral.add(new BoolSetting.Builder()
        .name("prefer-vital-areas")
        .description("Prefer targeting head and torso areas for more realistic aiming patterns.")
        .defaultValue(true)
        .visible(() -> enableRandomizedAiming.get())
        .build()
    );
    // --- End randomized aiming settings ---

    // --- Dynamic targeting settings ---
    private final Setting<Boolean> enableDynamicTargeting = sgGeneral.add(new BoolSetting.Builder()
        .name("dynamic-targeting")
        .description("Recalculate aiming area when mouse moves, making targeting more responsive to manual input.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> mouseMovementSensitivity = sgGeneral.add(new DoubleSetting.Builder()
        .name("mouse-sensitivity")
        .description("How sensitive the aimbot is to mouse movement (lower = more sensitive).")
        .defaultValue(0.5)
        .min(0.1)
        .max(2.0)
        .sliderMin(0.1)
        .sliderMax(2.0)
        .visible(() -> enableDynamicTargeting.get())
        .build()
    );

    private final Setting<Integer> targetRecalculateInterval = sgGeneral.add(new IntSetting.Builder()
        .name("recalculate-interval")
        .description("How often to recalculate target position in milliseconds.")
        .defaultValue(100)
        .min(50)
        .max(500)
        .sliderMin(50)
        .sliderMax(500)
        .visible(() -> enableDynamicTargeting.get())
        .build()
    );

    private final Setting<Boolean> avoidDirectPath = sgGeneral.add(new BoolSetting.Builder()
        .name("avoid-direct-path")
        .description("Avoid taking the most direct path to target, making movement more natural.")
        .defaultValue(true)
        .visible(() -> enableDynamicTargeting.get())
        .build()
    );

    private final Setting<Double> pathDeviation = sgGeneral.add(new DoubleSetting.Builder()
        .name("path-deviation")
        .description("How much to deviate from the direct path (0.0 = direct path, 1.0 = maximum deviation).")
        .defaultValue(0.3)
        .min(0.0)
        .max(1.0)
        .sliderMin(0.0)
        .sliderMax(1.0)
        .visible(() -> enableDynamicTargeting.get() && avoidDirectPath.get())
        .build()
    );
    // --- End dynamic targeting settings ---

    // --- Fields for logging control ---
    private long lastAnyLogTime = 0;
    private final long GLOBAL_LOG_RATE_LIMIT_MS = 100;

    private Vec3d lastLoggedPlayerPos = Vec3d.ZERO;
    private Vec3d lastLoggedTargetPos = Vec3d.ZERO;
    private float lastLoggedTargetYaw = 0;
    private float lastLoggedTargetPitch = 0;
    private String lastLoggedSimpleMessageContent = "";
    private boolean detailedLogInitialized = false; // <<< NEW: Flag to force first detailed log
    private final double POS_EPSILON = 0.05;
    private final float ANGLE_EPSILON = 0.5f;
    
    // --- Noise fields with fixed optimal values ---
    private long startTime = System.currentTimeMillis();
    private final double NOISE_SEED = Math.random() * 10000;
    
    // Using a simpler noise algorithm for more predictable behavior
    private static final double NOISE_AMPLITUDE = 0.0;
    private static final double NOISE_FREQUENCY = 0.05;
    // --- End noise fields ---

    // --- Fields for obstruction checking and target tracking ---
    private boolean isObstructed = false;
    private LivingEntity currentTarget = null;
    // --- End obstruction fields ---

    // --- Fields for mouse movement detection and dynamic targeting ---
    private float lastPlayerYaw = 0.0f;
    private float lastPlayerPitch = 0.0f;
    private boolean hasDetectedMouseMovement = false;
    private Vec3d cachedTargetPosition = null;
    private long lastTargetPositionUpdate = 0;
    private final long TARGET_POSITION_CACHE_MS = 100; // Recalculate target position every 100ms
    private final float MOUSE_MOVEMENT_THRESHOLD = 0.5f; // Minimum angle change to detect mouse movement

    // --- Fields for indirect path calculation ---
    private Vec3d intermediateTarget = null;
    private long lastIntermediateUpdate = 0;
    private final long INTERMEDIATE_UPDATE_INTERVAL = 200; // Update intermediate target every 200ms
    private double pathProgress = 0.0; // Progress along the indirect path (0.0 to 1.0)
    // --- End indirect path fields ---

    // --- End mouse movement fields ---

    // --- End fields ---
    
    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    public Aimbot() {
        super(AddonTemplate.CATEGORY, "aimbot", "Automatically aims at entities.");
    }

    @Override
    public void onDeactivate() {
        super.onDeactivate();
        detailedLogInitialized = false; // Reset when module deactivates
        isObstructed = false; // Reset obstruction state
        currentTarget = null; // Reset target
        // Reset mouse movement tracking
        hasDetectedMouseMovement = false;
        cachedTargetPosition = null;
        lastTargetPositionUpdate = 0;
        // Reset indirect path tracking
        intermediateTarget = null;
        lastIntermediateUpdate = 0;
        pathProgress = 0.0;
    }

    // --- New method for obstruction checking ---
    public boolean shouldBlockObstructedAttacks() {
        return blockObstructedAttacks.get();
    }

    public boolean isObstructed() {
        return isObstructed;
    }

    public LivingEntity getCurrentTarget() {
        return currentTarget;
    }
    // --- End new method ---

    // --- Mouse movement detection methods ---
    private boolean detectMouseMovement() {
        if (mc.player == null || !enableDynamicTargeting.get()) return false;

        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        // Calculate angle differences
        float yawDiff = Math.abs(currentYaw - lastPlayerYaw);
        float pitchDiff = Math.abs(currentPitch - lastPlayerPitch);

        // Normalize yaw difference to handle wrapping
        if (yawDiff > 180.0f) {
            yawDiff = 360.0f - yawDiff;
        }

        // Use configurable sensitivity threshold
        float threshold = MOUSE_MOVEMENT_THRESHOLD * mouseMovementSensitivity.get().floatValue();
        boolean mouseMovement = yawDiff > threshold || pitchDiff > threshold;

        // Update last known angles
        lastPlayerYaw = currentYaw;
        lastPlayerPitch = currentPitch;

        return mouseMovement;
    }

    private void invalidateTargetPosition() {
        cachedTargetPosition = null;
        lastTargetPositionUpdate = 0;
        hasDetectedMouseMovement = true;
        // Reset indirect path when target position is invalidated
        intermediateTarget = null;
        lastIntermediateUpdate = 0;
        pathProgress = 0.0;
    }
    // --- End mouse movement detection methods ---

    // --- Indirect path calculation methods ---
    private Vec3d calculateIndirectPath(Vec3d currentPos, Vec3d finalTarget, LivingEntity target) {
        if (!avoidDirectPath.get()) {
            return finalTarget; // Return direct path if setting is disabled
        }

        long currentTime = System.currentTimeMillis();

        // Update intermediate target periodically or when needed
        if (intermediateTarget == null ||
            (currentTime - lastIntermediateUpdate) > INTERMEDIATE_UPDATE_INTERVAL ||
            hasDetectedMouseMovement) {

            // Calculate a curved path to the target
            intermediateTarget = calculateCurvedIntermediatePoint(currentPos, finalTarget, target);
            lastIntermediateUpdate = currentTime;
            pathProgress = 0.0; // Reset progress when creating new path
        }

        // Progress along the indirect path
        pathProgress += 0.02; // Adjust speed of progression
        pathProgress = Math.min(pathProgress, 1.0);

        // Interpolate between current position direction and final target
        if (intermediateTarget != null) {
            // Create a smooth curve from current look direction -> intermediate -> final target
            Vec3d currentLookDirection = getPlayerLookDirection();
            Vec3d currentLookTarget = currentPos.add(currentLookDirection.multiply(5.0)); // Project current look 5 blocks ahead

            // Use quadratic bezier curve for smooth path
            return calculateBezierPoint(currentLookTarget, intermediateTarget, finalTarget, pathProgress);
        }

        return finalTarget; // Fallback to direct path
    }

    private Vec3d calculateCurvedIntermediatePoint(Vec3d playerPos, Vec3d targetPos, LivingEntity target) {
        // Get the entity's bounding box for more natural intermediate points
        Box boundingBox = target.getBoundingBox();
        Vec3d entityCenter = boundingBox.getCenter();

        // Calculate a point that's offset from the direct line to target
        Vec3d directVector = targetPos.subtract(playerPos).normalize();

        // Create perpendicular vectors for offset calculation
        Vec3d perpendicular1 = new Vec3d(-directVector.z, 0, directVector.x).normalize();
        Vec3d perpendicular2 = new Vec3d(0, 1, 0); // Vertical offset

        // Apply random offset based on deviation setting
        double deviation = pathDeviation.get();
        double horizontalOffset = (Math.random() - 0.5) * 2.0 * deviation * (boundingBox.maxX - boundingBox.minX);
        double verticalOffset = (Math.random() - 0.5) * 2.0 * deviation * (boundingBox.maxY - boundingBox.minY) * 0.5;

        // Calculate intermediate point with offsets
        Vec3d intermediatePoint = entityCenter
            .add(perpendicular1.multiply(horizontalOffset))
            .add(perpendicular2.multiply(verticalOffset));

        // Ensure the intermediate point is still within reasonable bounds of the entity
        intermediatePoint = new Vec3d(
            Math.max(boundingBox.minX, Math.min(boundingBox.maxX, intermediatePoint.x)),
            Math.max(boundingBox.minY, Math.min(boundingBox.maxY, intermediatePoint.y)),
            Math.max(boundingBox.minZ, Math.min(boundingBox.maxZ, intermediatePoint.z))
        );

        return intermediatePoint;
    }

    private Vec3d calculateBezierPoint(Vec3d p0, Vec3d p1, Vec3d p2, double t) {
        // Quadratic Bezier curve: B(t) = (1-t)²P0 + 2(1-t)tP1 + t²P2
        double oneMinusT = 1.0 - t;
        double oneMinusTSquared = oneMinusT * oneMinusT;
        double tSquared = t * t;
        double twoOneMinusTt = 2.0 * oneMinusT * t;

        return p0.multiply(oneMinusTSquared)
            .add(p1.multiply(twoOneMinusTt))
            .add(p2.multiply(tSquared));
    }

    private Vec3d getPlayerLookDirection() {
        if (mc.player == null) return Vec3d.ZERO;

        float yaw = (float) Math.toRadians(mc.player.getYaw());
        float pitch = (float) Math.toRadians(mc.player.getPitch());

        double x = -Math.sin(yaw) * Math.cos(pitch);
        double y = -Math.sin(pitch);
        double z = Math.cos(yaw) * Math.cos(pitch);

        return new Vec3d(x, y, z);
    }
    // --- End indirect path calculation methods ---

    private boolean isTargetInWeb(LivingEntity target) {
        // Check the target's block position and the block above it
        BlockPos targetPos = target.getBlockPos();
        return mc.world.getBlockState(targetPos).getBlock() == Blocks.COBWEB ||
               mc.world.getBlockState(targetPos.up()).getBlock() == Blocks.COBWEB;
    }

    private boolean isPathToTargetBlocked(LivingEntity target) {
        // Check if there are webs between player and target
        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = target.getEyePos();
        Vec3d direction = targetPos.subtract(playerPos).normalize();
        double distance = playerPos.distanceTo(targetPos);

        for (double d = 0; d < distance; d += 1.0) {
            Vec3d checkPos = playerPos.add(direction.multiply(d));
            BlockPos blockPos = new BlockPos((int)checkPos.x, (int)checkPos.y, (int)checkPos.z);
            if (mc.world.getBlockState(blockPos).getBlock() == Blocks.COBWEB) {
                return true;
            }
        }
        return false;
    }

    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || mc.world == null) {
            return;
        }

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }

        // Detect mouse movement and invalidate cached target position if needed (only if dynamic targeting is enabled)
        if (enableDynamicTargeting.get() && detectMouseMovement()) {
            invalidateTargetPosition();
        }

        if (onlyWhileLMBHeld.get()) {
            boolean lmbIsHeld = InputCheckUtils.isLMBHeld();
            if (lmbIsHeld) {

            } else {
                currentTarget = null; // Reset target when LMB is not held
                invalidateTargetPosition(); // Clear cached position when LMB released
                return;
            }
        }

        LivingEntity target = findTarget();
        if (target != null) {
            currentTarget = target; // Store current target
            aimAtEntity(target, event);
        } else {
            detailedLogInitialized = false; // Reset if no target found to force new detailed log when target reappears
            isObstructed = false; // Reset obstruction state when no target
            currentTarget = null; // Reset target
            invalidateTargetPosition(); // Clear cached position when no target
        }
    }

    private LivingEntity findTarget() {
        // If keep target mode is enabled and we have a valid current target, check if we should keep it
        if (keepTarget.get() && currentTarget != null) {
            // Check if target is still alive and within distance
            if (currentTarget.isAlive() && mc.player.distanceTo(currentTarget) <= keepTargetDistance.get()) {
                // Check if target is still within FOV if FOV is not 360
                if (fov.get() >= 360.0 || isWithinFOV(currentTarget)) {
                    return currentTarget; // Keep the same target
                }
            }
            // If we get here, we need to find a new target
            currentTarget = null;
        }

        Predicate<Entity> targetPredicate = entity -> {
            // EXCLUDE THE LOCAL PLAYER!
            if (entity.equals(mc.player)) {
                return false;
            }

            // Check if entity is a LivingEntity first
            if (!(entity instanceof LivingEntity)) {
                return false;
            }

            // Check if entity is in web or if path to entity is blocked by webs
            if (isTargetInWeb((LivingEntity)entity) || isPathToTargetBlocked((LivingEntity)entity)) {
                return false;
            }

            // Get AutoWeb module and check if target is going to be webbed

            Targets targetsModule = Modules.get().get(Targets.class);
            if (targetsModule == null || !targetsModule.isActive()) {
                // If the Targets module is not active or not found, use a default behavior or skip targeting.
                // For now, we'll skip targeting if the Targets module isn't active.
                return false;
            }

            // Use the new shouldTarget method to check if we should target this entity
            if (!targetsModule.shouldTarget(entity)) {
                return false;
            }

            double distance = mc.player.distanceTo(entity);
            boolean withinRange = distance <= range.get();
            boolean isLiving = entity instanceof LivingEntity;
            
            // Check if entity is within FOV
            boolean withinFOV = true;
            if (fov.get() < 360.0) {
                withinFOV = isWithinFOV(entity);
            }
            
            return withinRange && isLiving && withinFOV;
        };

        Entity target = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

        if (target instanceof LivingEntity) {
            return (LivingEntity) target;
        }
        return null;
    }

    // New method to check if an entity is within the FOV
    private boolean isWithinFOV(Entity entity) {
        if (mc.player == null) return false;

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = entity.getEyePos();

        double deltaX = targetPos.x - playerPos.x;
        double deltaY = targetPos.y - playerPos.y;
        double deltaZ = targetPos.z - playerPos.z;

        double distanceXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        // Calculate yaw and pitch to target
        float targetYaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(deltaY, distanceXZ));

        // Clamp pitch to valid range
        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        // Get current player angles
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        // Calculate shortest angle differences (no normalization to avoid jumps)
        float yawDiff = Math.abs(getShortestAngleDifference(currentYaw, targetYaw));
        float pitchDiff = Math.abs(targetPitch - currentPitch);

        // Check if within FOV
        return yawDiff <= fov.get() / 2.0 && pitchDiff <= fov.get() / 2.0;
    }
    
    // Helper method to calculate the shortest angle difference between two angles
    // This avoids sudden jumps when crossing the 360°/0° boundary
    private float getShortestAngleDifference(float angle1, float angle2) {
        float diff = angle2 - angle1;

        // Normalize difference to [-180, 180] range for shortest path
        while (diff > 180.0F) {
            diff -= 360.0F;
        }
        while (diff < -180.0F) {
            diff += 360.0F;
        }

        return diff;
    }

    private void aimAtEntity(LivingEntity target, Render3DEvent event) {
        // Check for block obstructions using the existing method
        isObstructed = RaycastUtils.hasBlockObstruction(target.getEyePos());

        if (isObstructed) {
            // Don't aim if obstructed and blocking is enabled
            if (blockObstructedAttacks.get()) {
                return;
            }
        }

        // Always use current entity positions (no caches)
        Vec3d playerPos = mc.player.getEyePos();

        // Use current player angles as starting point for smooth aiming
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float[] rotations;

        if (enableRandomizedAiming.get()) {
            if (enableDynamicTargeting.get()) {
                // Use dynamic randomized aiming that recalculates based on camera position
                rotations = calculateDynamicRandomizedRotations(
                    currentYaw,
                    currentPitch,
                    target,
                    playerPos,
                    aimSpeed.get(),
                    (float) event.frameTime
                );
            } else {
                // Use traditional randomized aiming
                rotations = SmoothAimingUtils.calculateSmartRandomizedSmoothRotations(
                    currentYaw,
                    currentPitch,
                    target,
                    playerPos,
                    aimSpeed.get(),
                    (float) event.frameTime,
                    randomizationStrength.get(),
                    proximityBias.get(),
                    preferVitalAreas.get()
                );
            }
        } else {
            // Use traditional aiming without randomization
            Vec3d targetPos = target.getEyePos(); // Use eye position for consistency with FOV check
            rotations = SmoothAimingUtils.calculateSmoothRotations(
                currentYaw,
                currentPitch,
                targetPos,
                playerPos,
                aimSpeed.get(),
                (float) event.frameTime
            );
        }

        // Apply the calculated rotations
        mc.player.setYaw(rotations[0]);
        mc.player.setPitch(rotations[1]);
    }

    // --- Dynamic targeting method ---
    private float[] calculateDynamicRandomizedRotations(float currentYaw, float currentPitch,
                                                       LivingEntity target, Vec3d playerPos,
                                                       double aimSpeed, float deltaTime) {
        long currentTime = System.currentTimeMillis();

        // Check if we need to recalculate the target position
        boolean shouldRecalculate = cachedTargetPosition == null ||
                                   hasDetectedMouseMovement ||
                                   (currentTime - lastTargetPositionUpdate) > targetRecalculateInterval.get();

        if (shouldRecalculate) {
            // Recalculate target position based on current camera position
            Vec3d baseTargetPosition;
            if (enableRandomizedAiming.get()) {
                baseTargetPosition = RandomizedAimingUtils.getSmartRandomizedTargetPosition(
                    target,
                    playerPos,
                    randomizationStrength.get(),
                    proximityBias.get(),
                    preferVitalAreas.get()
                );
            } else {
                baseTargetPosition = target.getEyePos();
            }

            // Apply indirect path calculation to avoid direct targeting
            cachedTargetPosition = calculateIndirectPath(playerPos, baseTargetPosition, target);

            lastTargetPositionUpdate = currentTime;
            hasDetectedMouseMovement = false; // Reset flag after recalculation
        } else if (avoidDirectPath.get()) {
            // Update the indirect path even if we're not recalculating the base target
            Vec3d baseTarget = cachedTargetPosition;
            if (baseTarget != null) {
                cachedTargetPosition = calculateIndirectPath(playerPos, baseTarget, target);
            }
        }

        // Use the cached target position for smooth aiming
        if (cachedTargetPosition != null) {
            return SmoothAimingUtils.calculateSmoothRotations(
                currentYaw,
                currentPitch,
                cachedTargetPosition,
                playerPos,
                aimSpeed,
                deltaTime
            );
        } else {
            // Fallback to center targeting if cached position is null
            return SmoothAimingUtils.calculateSmoothRotations(
                currentYaw,
                currentPitch,
                target.getEyePos(),
                playerPos,
                aimSpeed,
                deltaTime
            );
        }
    }
    // --- End dynamic targeting method ---
}