package com.example.addon.utils;

import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import java.util.Random;

/**
 * Utility class for generating noise patterns including Fractal Brownian Motion (FBM)
 * to make movements more human-like in aimbots and other modules.
 */
public class NoiseUtils {
    private static final Random random = new Random();

    /**
     * Generate a pseudo-random number using a sine-based hash function
     * @param x X coordinate
     * @param y Y coordinate
     * @param seed Randomization seed
     * @return Noise value between -1 and 1
     */
    public static double noise(double x, double y, double seed) {
        return MathHelper.sin((float) (x * 12.9898 + y * 78.233 + seed) * 43758.5453f) * 2.0 - 1.0;
    }

    /**
     * Linear interpolation between two values
     * @param a First value
     * @param b Second value
     * @param t Interpolation factor (0-1)
     * @return Interpolated value
     */
    public static double lerp(double a, double b, double t) {
        return a + t * (b - a);
    }

    /**
     * Smooth interpolation using cubic function
     * @param t Interpolation factor (0-1)
     * @return Smoothed value
     */
    public static double smoothstep(double t) {
        return t * t * (3 - 2 * t);
    }

    /**
     * Generate 2D Perlin-like noise at given coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @param seed Randomization seed
     * @return Smooth noise value between -1 and 1
     */
    public static double smoothNoise(double x, double y, double seed) {
        double corners = (noise(x - 1, y - 1, seed) + noise(x + 1, y - 1, seed) + 
                         noise(x - 1, y + 1, seed) + noise(x + 1, y + 1, seed)) / 16;
        double sides = (noise(x - 1, y, seed) + noise(x + 1, y, seed) + 
                       noise(x, y - 1, seed) + noise(x, y + 1, seed)) / 8;
        double center = noise(x, y, seed) / 4;
        return corners + sides + center;
    }

    /**
     * Interpolated noise function
     * @param x X coordinate
     * @param y Y coordinate
     * @param seed Randomization seed
     * @return Interpolated noise value
     */
    public static double interpolatedNoise(double x, double y, double seed) {
        int intX = (int) Math.floor(x);
        double fracX = x - intX;
        
        int intY = (int) Math.floor(y);
        double fracY = y - intY;
        
        double v1 = smoothNoise(intX, intY, seed);
        double v2 = smoothNoise(intX + 1, intY, seed);
        double v3 = smoothNoise(intX, intY + 1, seed);
        double v4 = smoothNoise(intX + 1, intY + 1, seed);
        
        double i1 = lerp(v1, v2, fracX);
        double i2 = lerp(v3, v4, fracX);
        
        return lerp(i1, i2, fracY);
    }

    /**
     * Generate Fractal Brownian Motion (FBM) using multiple octaves of noise
     * @param x X coordinate
     * @param y Y coordinate
     * @param octaves Number of noise layers
     * @param persistence Amplitude multiplier per octave
     * @param lacunarity Frequency multiplier per octave
     * @param seed Randomization seed
     * @return FBM value typically between -1 and 1
     */
    public static double fbm(double x, double y, int octaves, double persistence, double lacunarity, double seed) {
        double total = 0;
        double frequency = 1;
        double amplitude = 1;
        double maxValue = 0; // Used for normalizing result to [-1, 1]
        
        for (int i = 0; i < octaves; i++) {
            total += interpolatedNoise(x * frequency, y * frequency, seed + i) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return total / maxValue;
    }

    /**
     * Generate 3D noise vector using FBM for natural-looking deviations
     * @param time Time parameter for animation
     * @param scale Scaling factor for the noise
     * @param octaves Number of noise layers
     * @param persistence Amplitude multiplier per octave
     * @param lacunarity Frequency multiplier per octave
     * @param seed Randomization seed
     * @return Vector with noise values for X, Y, Z axes
     */
    public static Vec3d generateFBMVector(double time, double scale, int octaves, double persistence, double lacunarity, double seed) {
        double x = fbm(time * scale, 0, octaves, persistence, lacunarity, seed) * scale;
        double y = fbm(time * scale, 1000, octaves, persistence, lacunarity, seed + 1000) * scale;
        double z = fbm(time * scale, 2000, octaves, persistence, lacunarity, seed + 2000) * scale;
        return new Vec3d(x, y, z);
    }

    /**
     * Generate a random vector with components between -1 and 1
     * @return Random vector
     */
    public static Vec3d randomVector() {
        return new Vec3d(
            random.nextGaussian(),
            random.nextGaussian(),
            random.nextGaussian()
        ).normalize();
    }
}