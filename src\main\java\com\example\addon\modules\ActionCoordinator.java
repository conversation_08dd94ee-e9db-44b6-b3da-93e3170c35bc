package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;

public class ActionCoordinator extends Module {
    public ActionCoordinator() {
        super(AddonTemplate.CATEGORY, "action-coordinator", "Coordinates actions between different combat modules to prevent conflicts.");
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        // Reset the action taken flag at the end of each tick
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Method resetMethod = maceAuraClass.getMethod("resetActionTaken");
            resetMethod.invoke(null);
        } catch (Exception e) {
            // Ignore if we can't reset the flag
        }
    }
}