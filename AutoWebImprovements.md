# AutoWeb Sophisticated Algorithm Improvements

## Overview
The AutoWeb module has been enhanced with a sophisticated placement algorithm that addresses your specific requirements:

1. **Hit an enemy** - Improved target acquisition and tracking
2. **Catch them in a web** - Advanced physics-based movement prediction
3. **Use as few webs as possible** - Smart scoring system to minimize web usage
4. **Preferably catch head/upper body** - Prioritized head-level placement

## Key Improvements

### 1. Physics-Based Movement Prediction
- **Gravity simulation**: Accounts for falling targets with realistic gravity (0.08 blocks/tick)
- **Air resistance**: Considers velocity decay over time
- **Landing prediction**: Predicts when and where targets will land
- **Confidence scoring**: Rates prediction accuracy based on target velocity and behavior

### 2. Advanced Scoring System
The algorithm scores placement candidates based on multiple factors:

- **Head capture bonus**: +3.0 points for head-level positions (configurable)
- **Prediction confidence**: Up to +2.0 points for high-confidence predictions
- **Distance optimization**: Penalty for positions too far from player
- **Velocity consideration**: Penalty for fast-moving targets
- **Strategic positioning**: Additional positions around slow targets

### 3. Smart Candidate Generation
- **Current positions**: Target's immediate head and feet positions
- **Predicted positions**: Where the target will be based on physics simulation
- **Strategic positions**: Surrounding positions for slow/stationary targets
- **Fallback options**: Multiple alternatives if primary positions fail

### 4. Configurable Settings
New settings added to the AutoWeb module:

- `sophisticated-algorithm`: Enable/disable the new algorithm
- `prediction-confidence`: Minimum confidence threshold (0.0-1.0)
- `prioritize-head-capture`: Boost head-level placement scores
- `debug-mode`: Print detailed placement decisions to console

## Algorithm Flow

1. **Target Analysis**: Analyze target's current position, velocity, and state
2. **Movement Prediction**: Simulate target movement using physics
3. **Candidate Generation**: Create scored list of potential placement positions
4. **Ranking**: Sort candidates by effectiveness score
5. **Validation**: Check if top candidates can actually be placed
6. **Placement**: Execute placement at best valid position
7. **Fallback**: Use original algorithm if sophisticated method fails

## Benefits

### Efficiency
- **Reduced web usage**: Smart scoring minimizes unnecessary placements
- **Higher success rate**: Physics prediction improves hit probability
- **Better positioning**: Prioritizes most effective capture points

### Combat Effectiveness
- **Head prioritization**: Focuses on upper body capture as requested
- **Movement anticipation**: Leads moving targets effectively
- **Strategic trapping**: Places webs to create effective traps

### Reliability
- **Fallback system**: Original algorithm available if new one fails
- **Configurable**: Can be tuned or disabled based on preference
- **Debug support**: Detailed logging for troubleshooting

## Usage

1. Enable the module as usual
2. Set `sophisticated-algorithm` to `true` (default)
3. Adjust `prediction-confidence` based on your preference (0.3 default)
4. Enable `prioritize-head-capture` for upper body focus (default: true)
5. Use `debug-mode` to see algorithm decisions in console

The algorithm automatically falls back to the original placement method if the sophisticated approach fails, ensuring reliability while providing enhanced performance when possible.
