# AutoWeb Sophisticated Algorithm Improvements

## Overview
The AutoWeb module has been enhanced with a sophisticated placement algorithm that addresses your specific requirements:

1. **Hit an enemy** - Improved target acquisition and tracking
2. **Catch them in a web** - **LANDING-FOCUSED** physics-based movement prediction
3. **Use as few webs as possible** - Smart scoring system to minimize web usage
4. **Preferably catch head/upper body** - Prioritized head-level placement **WHERE THEY WILL LAND**

## 🎯 KEY FOCUS: ACTUAL LANDING PREDICTION
The algorithm now **ONLY places webs where targets will actually LAND** - not just a time delay ahead, but their true landing spot after physics simulation.

## 🔧 CRITICAL FIXES IMPLEMENTED
1. **✅ Swaps back to original item** - Automatically returns to your previous item after placing
2. **✅ Never affects the player** - Advanced self-protection prevents web placement that would trap you
3. **✅ Places where they LAND** - True landing detection, not just trajectory prediction

## Key Improvements

### 1. TRUE Landing Detection (Not Just Trajectory)
- **Actual landing detection**: Simulates physics until target hits solid ground
- **Gravity simulation**: Realistic gravity (0.08 blocks/tick) with terminal velocity
- **Drag simulation**: Minecraft's 0.98 drag factor for accurate movement
- **Ground collision**: Detects when target will actually hit a solid block
- **Extended simulation**: Up to 60 ticks ahead for thorough landing detection
- **Landing position adjustment**: Places web ON the ground surface, not inside blocks
- **Smart confidence**: 95% confidence for immediate landings, scaling down for distant ones

### 2. Landing-Prioritized Scoring System
The algorithm heavily prioritizes landing positions with these scoring factors:

- **Landing bonus**: +5.0 points for predicted landing positions (HUGE bonus)
- **Head capture bonus**: +4.0 points for head-level positions at landing spot
- **Prediction confidence**: Up to +3.0 points for high-confidence landing predictions
- **Distance optimization**: Reduced penalty for landing positions (they're worth the reach)
- **Current position penalty**: -30.0 points for current positions when target will land
- **Landing area coverage**: Additional positions around landing spot for better trapping

### 3. Landing-Focused Candidate Generation
- **Landing positions**: **HIGHEST PRIORITY** - Head and feet positions where target will land
- **Landing area**: Positions around the landing spot for comprehensive coverage
- **Current positions**: **LOWER PRIORITY** - Only used when landing prediction unavailable
- **Predicted positions**: Non-landing trajectory positions (medium priority)
- **Strategic positions**: Surrounding positions for stationary targets (lowest priority)

### 4. Configurable Settings
New settings added to the AutoWeb module:

- `sophisticated-algorithm`: Enable/disable the new algorithm
- `prediction-confidence`: Minimum confidence threshold (0.0-1.0)
- `prioritize-head-capture`: Boost head-level placement scores
- `debug-mode`: Print detailed placement decisions to console

## Algorithm Flow

1. **Target Analysis**: Analyze target's current position, velocity, and state
2. **Movement Prediction**: Simulate target movement using physics
3. **Candidate Generation**: Create scored list of potential placement positions
4. **Ranking**: Sort candidates by effectiveness score
5. **Validation**: Check if top candidates can actually be placed
6. **Placement**: Execute placement at best valid position
7. **Fallback**: Use original algorithm if sophisticated method fails

## Benefits

### Efficiency
- **Reduced web usage**: Smart scoring minimizes unnecessary placements
- **Higher success rate**: Physics prediction improves hit probability
- **Better positioning**: Prioritizes most effective capture points

### Combat Effectiveness
- **Head prioritization**: Focuses on upper body capture as requested
- **Movement anticipation**: Leads moving targets effectively
- **Strategic trapping**: Places webs to create effective traps

### Reliability
- **Fallback system**: Original algorithm available if new one fails
- **Configurable**: Can be tuned or disabled based on preference
- **Debug support**: Detailed logging for troubleshooting

## Usage

1. Enable the module as usual
2. Set `sophisticated-algorithm` to `true` (default)
3. Adjust `prediction-confidence` based on your preference (0.3 default)
4. Enable `prioritize-head-capture` for upper body focus (default: true)
5. Use `debug-mode` to see algorithm decisions in console

The algorithm automatically falls back to the original placement method if the sophisticated approach fails, ensuring reliability while providing enhanced performance when possible.
