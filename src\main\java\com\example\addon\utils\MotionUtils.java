package com.example.addon.utils;

import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;

public class MotionUtils {

    private static final MinecraftClient mc = MinecraftClient.getInstance();

    public static Vec3d predictPlayerPosition(PlayerEntity player, int ticks) {
        if (player == null) {
            return null;
        }

        Vec3d currentPos = player.getPos();
        Vec3d currentVel = player.getVelocity();

        // Simulate motion tick by tick
        for (int i = 0; i < ticks; i++) {
            currentPos = currentPos.add(currentVel);
            // Apply gravity
            currentVel = currentVel.add(0, -0.08, 0);
            // Apply air resistance (horizontal and vertical)
            currentVel = currentVel.multiply(0.98, 0.98, 0.98);
        }

        return currentPos;
    }

    public static Vec3d predictEntityPosition(net.minecraft.entity.Entity entity, int ticks) {
        if (entity == null) {
            return null;
        }

        Vec3d currentPos = entity.getPos();
        Vec3d currentVel = entity.getVelocity();

        for (int i = 0; i < ticks; i++) {
            currentPos = currentPos.add(currentVel);
            currentVel = currentVel.add(0, -0.08, 0);
            currentVel = currentVel.multiply(0.98, 0.98, 0.98);
        }
        return currentPos;
    }

    public static int getTicksToFall(PlayerEntity player, double fallDistance) {
        if (player == null) {
            return 0;
        }

        double currentFallDistance = 0;
        int ticks = 0;
        Vec3d currentVelocity = player.getVelocity();

        while (currentFallDistance < fallDistance) {
            currentFallDistance -= currentVelocity.y;
            currentVelocity = currentVelocity.add(0, -0.08, 0);
            currentVelocity = currentVelocity.multiply(0.98, 0.98, 0.98);
            ticks++;
        }

        return ticks;
    }
}