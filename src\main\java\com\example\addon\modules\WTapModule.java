package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.ModuleCommunication;
import meteordevelopment.meteorclient.events.entity.player.AttackEntityEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.Hand;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class WTapModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgDistance = settings.createGroup("Distance Control");
    private final SettingGroup sgSTap = settings.createGroup("S-Tap");
    private final SettingGroup sgSprint = settings.createGroup("Auto Sprint");

    private final Setting<Mode> mode = sgGeneral.add(new EnumSetting.Builder<Mode>()
        .name("mode")
        .description("W-Tap mode.")
        .defaultValue(Mode.Dynamic)
        .build()
    );

    private final Setting<Boolean> onlyWhenWHeld = sgGeneral.add(new BoolSetting.Builder()
        .name("only-when-w-held")
        .description("Only perform W-Tap when W key is held by user.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> detailedLogging = sgGeneral.add(new BoolSetting.Builder()
        .name("detailed-logging")
        .description("Enable detailed logging for debugging.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> tapDelay = sgGeneral.add(new IntSetting.Builder()
        .name("tap-delay")
        .description("Delay in ticks before releasing W after an attack.")
        .defaultValue(0)
        .min(0)
        .max(2)
        .visible(() -> mode.get() == Mode.Normal)
        .build()
    );

    private final Setting<Integer> tapDuration = sgGeneral.add(new IntSetting.Builder()
        .name("tap-duration")
        .description("Duration in ticks to hold W after releasing.")
        .defaultValue(1)
        .min(0)
        .max(3)
        .visible(() -> mode.get() == Mode.Normal)
        .build()
    );

    // Distance control settings
    private final Setting<Double> targetDistance = sgDistance.add(new DoubleSetting.Builder()
        .name("target-distance")
        .description("Target distance to maintain from enemies (in blocks).")
        .defaultValue(3.0)
        .range(2.5, 4.0)
        .sliderRange(2.5, 4.0)
        .visible(() -> mode.get() == Mode.Dynamic)
        .build()
    );

    private final Setting<Double> attackDistance = sgDistance.add(new DoubleSetting.Builder()
        .name("attack-distance")
        .description("Distance threshold for attacking (<= this distance triggers attack behavior).")
        .defaultValue(2.9)
        .range(2.0, 3.5)
        .sliderRange(2.0, 3.5)
        .visible(() -> mode.get() == Mode.Dynamic)
        .build()
    );

    private final Setting<Double> spacingDistance = sgDistance.add(new DoubleSetting.Builder()
        .name("spacing-distance")
        .description("Distance threshold for spacing (> this distance triggers spacing behavior).")
        .defaultValue(3.5)
        .range(2.5, 5.0)
        .sliderRange(2.5, 5.0)
        .visible(() -> mode.get() == Mode.Dynamic)
        .build()
    );

    private final Setting<Double> maxTrackingDistance = sgDistance.add(new DoubleSetting.Builder()
        .name("max-tracking-distance")
        .description("Maximum distance to track and respond to enemies.")
        .defaultValue(6.0)
        .range(4.0, 10.0)
        .sliderRange(4.0, 10.0)
        .visible(() -> mode.get() == Mode.Dynamic)
        .build()
    );

    // S-Tap settings
    private final Setting<Boolean> enableSTap = sgSTap.add(new BoolSetting.Builder()
        .name("enable-s-tap")
        .description("Enable S-Tap functionality for spacing control.")
        .defaultValue(true)
        .build()
    );



    private final Setting<Integer> sTapDuration = sgSTap.add(new IntSetting.Builder()
        .name("s-tap-duration")
        .description("How long to hold S for spacing (in ticks).")
        .defaultValue(5)
        .min(1)
        .max(10)
        .visible(() -> enableSTap.get())
        .build()
    );

    private final Setting<Double> sTapDistance = sgSTap.add(new DoubleSetting.Builder()
        .name("s-tap-distance")
        .description("Distance threshold to trigger S-Tap (activate when target closer than this).")
        .defaultValue(2.5)
        .range(2.0, 3.5)
        .visible(() -> enableSTap.get())
        .build()
    );

    // Auto Sprint settings
    private final Setting<Boolean> enableAutoSprint = sgSprint.add(new BoolSetting.Builder()
        .name("enable-auto-sprint")
        .description("Automatically maintain sprinting while moving forward.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> onlyWhenMoving = sgSprint.add(new BoolSetting.Builder()
        .name("only-when-moving")
        .description("Only auto-sprint when actually moving forward.")
        .defaultValue(true)
        .visible(() -> enableAutoSprint.get())
        .build()
    );

    // State management
    private enum TapState {
        IDLE,
        RELEASE_PHASE,
        PRESS_PHASE,
        SPACING_PHASE,
        S_TAP_ACTIVE,
        DISTANCE_CONTROL
    }

    private TapState currentState = TapState.IDLE;
    private int tapTimer = 0;
    private Entity lastAttackedEntity = null;
    private Entity currentTarget = null;
    private boolean forceReleaseW = false;

    private boolean sTapTriggered = false;
    private int sTapCooldown = 0;
    private long lastDistanceCheck = 0;
    private double lastDistance = 0;
    private boolean isControllingDistance = false;
    private boolean sendingPacket = false;

    public enum Mode {
        Normal,
        Dynamic
    }

    public WTapModule() {
        super(AddonTemplate.CATEGORY, "w-tap", "Improved W-Tap with S-Tap spacing and auto-sprint for optimal PvP mechanics.");
    }

    @Override
    public void onDeactivate() {
        // Always release both W and S when module is deactivated
        if (mc.options != null) {
            if (mc.options.forwardKey != null) {
                mc.options.forwardKey.setPressed(false);
            }
            if (mc.options.backKey != null) {
                mc.options.backKey.setPressed(false);
            }
        }
        
        // Stop auto-sprinting by releasing sprint key
        if (mc.player != null && mc.options != null && mc.options.sprintKey != null) {
            mc.options.sprintKey.setPressed(false);
        }
        
        resetState();
    }

    private void resetState() {
        currentState = TapState.IDLE;
        tapTimer = 0;
        lastAttackedEntity = null;
        forceReleaseW = false;
        sTapTriggered = false;
        isControllingDistance = false;

        // Reset key states when sequence completes
        if (mc.options != null) {
            if (mc.options.forwardKey != null) {
                mc.options.forwardKey.setPressed(false);
            }
            if (mc.options.backKey != null) {
                mc.options.backKey.setPressed(false);
            }
            if (mc.options.sprintKey != null) {
                mc.options.sprintKey.setPressed(false);
            }
        }
    }

    @EventHandler
    private void onAttackEntity(AttackEntityEvent event) {
        if (mc.player == null || mc.options == null || mc.options.forwardKey == null) return;
        
        // Check if W-Tap should only activate when W is held
        if (onlyWhenWHeld.get() && !InputCheckUtils.isWKeyPressed()) {
            return;
        }
        
        // Only perform W-Tap if actually sprinting (for sprint reset)
        if (!mc.player.isSprinting()) {
            return;
        }
        
        // W-tap sequence starting
        
        // Check if S-Tap should be triggered based on distance (with cooldown)
        sTapTriggered = false;
        if (enableSTap.get() && event.entity instanceof LivingEntity && sTapCooldown <= 0) {
            double distance = mc.player.distanceTo(event.entity);
            if (distance <= sTapDistance.get()) {
                sTapTriggered = true;
                sTapCooldown = 10; // 0.5 second cooldown
                info("S-Tap will be triggered after W-Tap (distance: " + String.format("%.2f", distance) + ")");
            }
        }
        
        lastAttackedEntity = event.entity;
        
        if (mode.get() == Mode.Normal) {
            markActionTaken(); // Notify action coordinator
            initiateNormalWTap();
        } else if (mode.get() == Mode.Dynamic) {
            markActionTaken(); // Notify action coordinator
            initiateDynamicWTap(event.entity);
        }
    }

    private void initiateNormalWTap() {
        currentState = TapState.RELEASE_PHASE;
        tapTimer = 0;
        info("Normal W-Tap initiated");
    }

    private void initiateDynamicWTap(Entity targetEntity) {
        if (!(targetEntity instanceof LivingEntity)) return;

        double distance = mc.player.distanceTo(targetEntity);
        currentTarget = targetEntity;

        if (distance <= attackDistance.get()) {
            // Close range - quick sprint reset
            currentState = TapState.RELEASE_PHASE;
            tapTimer = 0;
            if (detailedLogging.get()) info("Dynamic W-Tap: Sprint reset at distance " + String.format("%.2f", distance));
        } else if (distance > spacingDistance.get()) {
            // Far range - need to close distance after attack
            currentState = TapState.SPACING_PHASE;
            tapTimer = 0;
            if (detailedLogging.get()) info("Dynamic W-Tap: Closing distance at " + String.format("%.2f", distance));
        } else {
            // Optimal range - standard tap to maintain sprint
            currentState = TapState.RELEASE_PHASE;
            tapTimer = 0;
            if (detailedLogging.get()) info("Dynamic W-Tap: Optimal range at distance " + String.format("%.2f", distance));
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.options == null || mc.options.forwardKey == null) return;

        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }

        // Handle sprint reset requests from AutoClicker
        handleSprintResetRequests();

        // Handle S-Tap cooldown
        if (sTapCooldown > 0) sTapCooldown--;

        // Handle auto-sprint
        handleAutoSprint();

        // Handle forced W release
        if (forceReleaseW) {
            mc.options.forwardKey.setPressed(false);
            forceReleaseW = false;
            if (detailedLogging.get()) info("Forced W release");
            return;
        }

        // Dynamic distance control (only in Dynamic mode)
        if (mode.get() == Mode.Dynamic) {
            handleDynamicDistanceControl();
        }

        switch (currentState) {
            case IDLE:
                // Do nothing
                break;

            case RELEASE_PHASE:
                handleReleasePhase();
                break;

            case PRESS_PHASE:
                handlePressPhase();
                break;

            case SPACING_PHASE:
                handleSpacingPhase();
                break;

            case S_TAP_ACTIVE:
                handleSTapActive();
                break;

            case DISTANCE_CONTROL:
                handleDistanceControl();
                break;
        }

        if (currentState != TapState.IDLE) {
            tapTimer++;
        }
    }

    private void handleSprintResetRequests() {
        // Check for sprint reset requests from AutoClicker
        if (ModuleCommunication.isSprintResetRequested()) {
            Entity requestTarget = ModuleCommunication.getSprintResetTarget();

            // Only perform sprint reset if we're currently sprinting
            if (mc.player.isSprinting()) {
                // Perform sprint reset: briefly release sprint, then re-engage
                mc.options.sprintKey.setPressed(false);

                // Immediately re-engage sprint for continuous forward momentum
                // This creates the sprint reset effect while maintaining attack flow
                mc.options.sprintKey.setPressed(true);

                if (detailedLogging.get()) {
                    String targetName = requestTarget != null ? requestTarget.getName().getString() : "unknown";
                    info("Sprint reset performed for AutoClicker attack on: " + targetName);
                }

                markActionTaken(); // Mark that we've taken action this tick
            }

            // Clear the request after handling
            ModuleCommunication.clearSprintResetRequest();
        }
    }

    private void handleAutoSprint() {
        if (!enableAutoSprint.get() || mc.player == null) return;

        // Check if we should be sprinting
        boolean shouldSprint = false;

        if (onlyWhenMoving.get()) {
            // Only sprint when actually moving forward
            double horizontalSpeed = Math.sqrt(mc.player.getVelocity().x * mc.player.getVelocity().x +
                                            mc.player.getVelocity().z * mc.player.getVelocity().z);
            shouldSprint = InputCheckUtils.isWKeyPressed() &&
                        horizontalSpeed > 0.01 &&
                        mc.player.getHungerManager().getFoodLevel() > 6;
        } else {
            // Sprint whenever W is held
            shouldSprint = InputCheckUtils.isWKeyPressed() &&
                        mc.player.getHungerManager().getFoodLevel() > 6;
        }

        // Apply sprinting state using actual sprint key
        if (shouldSprint && !mc.player.isSprinting()) {
            // Use the actual sprint key instead of just setting sprint state
            mc.options.sprintKey.setPressed(true);
        } else if (!shouldSprint && mc.player.isSprinting()) {
            // Release sprint key when we shouldn't be sprinting
            mc.options.sprintKey.setPressed(false);
        }
    }

    private void handleReleasePhase() {
        if (tapTimer == 0) {
            // Immediately release W
            mc.options.forwardKey.setPressed(false);
            if (detailedLogging.get()) info("Released W (Phase: RELEASE)");
        }

        int delay = tapDelay.get();

        if (tapTimer >= delay) {
            currentState = TapState.PRESS_PHASE;
            tapTimer = 0;
        }
    }

    private void handlePressPhase() {
        if (tapTimer == 0) {
            // Press W again and ensure sprinting
            mc.options.forwardKey.setPressed(true);
            if (enableAutoSprint.get()) {
                mc.options.sprintKey.setPressed(true);
            }
            if (detailedLogging.get()) info("Pressed W again (Phase: PRESS)");
        }

        int duration = tapDuration.get();

        if (tapTimer >= duration) {
            // Check if S-Tap should be performed
            if (sTapTriggered) {
                currentState = TapState.S_TAP_ACTIVE;
                tapTimer = 0;
                if (detailedLogging.get()) info("W-Tap complete, starting S-Tap immediately");
            } else {
                // Complete the sequence
                if (detailedLogging.get()) info("W-Tap sequence complete");
                resetState();
            }
        }
    }

    private void handleSpacingPhase() {
        if (tapTimer == 0) {
            // Initially release W for sprint reset
            mc.options.forwardKey.setPressed(false);
            if (detailedLogging.get()) info("Released W for spacing");
        } else if (tapTimer == 1) {
            // Immediately press W again to maintain forward momentum
            mc.options.forwardKey.setPressed(true);
            if (enableAutoSprint.get()) {
                mc.options.sprintKey.setPressed(true);
            }
            if (detailedLogging.get()) info("Pressing W for spacing");
        } else if (tapTimer >= 1) {
            // Check current distance to target more aggressively
            if (currentTarget != null) {
                double distance = mc.player.distanceTo(currentTarget);
                if (distance <= targetDistance.get() + 0.5) {
                    // Close enough, complete spacing
                    if (sTapTriggered) {
                        currentState = TapState.S_TAP_ACTIVE;
                        tapTimer = 0;
                        if (detailedLogging.get()) info("Spacing complete, starting S-Tap immediately");
                    } else {
                        if (detailedLogging.get()) info("Spacing complete");
                        resetState();
                    }
                    return;
                }
            }

            // Continue spacing if still too far or no target - reduced max time
            if (tapTimer >= 10) { // Max 0.5 second of spacing (more aggressive)
                if (sTapTriggered) {
                    currentState = TapState.S_TAP_ACTIVE;
                    tapTimer = 0;
                    if (detailedLogging.get()) info("Max spacing time reached, starting S-Tap immediately");
                } else {
                    if (detailedLogging.get()) info("Max spacing time reached");
                    resetState();
                }
            }
        }
    }



    private void handleSTapActive() {
        if (tapTimer == 0) {
            // Start S-Tap by pressing S
            mc.options.backKey.setPressed(true);
            info("S-Tap activated - pressing S for spacing");
        }
        
        if (tapTimer >= sTapDuration.get()) {
            // Complete S-Tap
            mc.options.backKey.setPressed(false);
            info("S-Tap complete");
            resetState();
        }
    }

    private void handleDynamicDistanceControl() {
        // Find the closest enemy player
        currentTarget = findClosestEnemy();

        if (currentTarget == null) {
            // No target found, stop distance control
            if (isControllingDistance) {
                isControllingDistance = false;
                if (detailedLogging.get()) info("No target found, stopping distance control");
            }
            return;
        }

        double distance = mc.player.distanceTo(currentTarget);
        lastDistance = distance;
        lastDistanceCheck = System.currentTimeMillis();

        // Only control distance if we're not in an active W-Tap sequence
        if (currentState == TapState.IDLE) {
            handleDistanceBasedMovement(distance);
        }
    }

    private Entity findClosestEnemy() {
        Entity closestEnemy = null;
        double closestDistance = maxTrackingDistance.get();

        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof PlayerEntity) || entity == mc.player) continue;

            PlayerEntity player = (PlayerEntity) entity;
            if (player.isDead() || player.getHealth() <= 0) continue;

            double distance = mc.player.distanceTo(player);
            if (distance < closestDistance) {
                closestDistance = distance;
                closestEnemy = player;
            }
        }

        return closestEnemy;
    }

    private void handleDistanceBasedMovement(double distance) {
        double target = targetDistance.get();
        double tolerance = 0.2; // 0.2 block tolerance

        if (distance < attackDistance.get()) {
            // Too close - need to back away (S-tap or stop W)
            if (enableSTap.get() && sTapCooldown <= 0) {
                triggerSTap();
            } else {
                // Just stop moving forward
                if (InputCheckUtils.isWKeyPressed()) {
                    mc.options.forwardKey.setPressed(false);
                    isControllingDistance = true;
                    if (detailedLogging.get()) info("Too close (" + String.format("%.2f", distance) + "), stopping forward movement");
                }
            }
        } else if (distance > spacingDistance.get()) {
            // Too far - need to move forward
            if (!InputCheckUtils.isWKeyPressed() || !mc.player.isSprinting()) {
                mc.options.forwardKey.setPressed(true);
                if (enableAutoSprint.get()) {
                    mc.options.sprintKey.setPressed(true);
                }
                isControllingDistance = true;
                if (detailedLogging.get()) info("Too far (" + String.format("%.2f", distance) + "), moving forward");
            }
        } else {
            // In optimal range - maintain current state but ensure sprinting if moving
            if (isControllingDistance) {
                // Restore original W key state
                mc.options.forwardKey.setPressed(InputCheckUtils.isWKeyPressed());
                isControllingDistance = false;
                if (detailedLogging.get()) info("Optimal distance (" + String.format("%.2f", distance) + "), restoring normal movement");
            }
        }
    }

    private void triggerSTap() {
        if (currentState != TapState.IDLE) return;

        currentState = TapState.S_TAP_ACTIVE;
        tapTimer = 0;
        sTapCooldown = 20; // 1 second cooldown
        sTapTriggered = true;

        if (detailedLogging.get()) info("Distance-triggered S-Tap activated");
    }

    private void handleDistanceControl() {
        // This state is for continuous distance monitoring during combat
        if (currentTarget == null) {
            resetState();
            return;
        }

        double distance = mc.player.distanceTo(currentTarget);
        handleDistanceBasedMovement(distance);

        // Exit distance control after a reasonable time
        if (tapTimer > 60) { // 3 seconds
            resetState();
        }
    }

    // Method to force W release if needed (can be called by other modules)
    public void forceReleaseW() {
        this.forceReleaseW = true;
        resetState();
    }

    // Method to check if W-tap is currently controlling movement
    public boolean isControllingMovement() {
        return currentState != TapState.IDLE || isControllingDistance;
    }

    @EventHandler
    private void onPacketSend(PacketEvent.Send event) {
        // Prevent infinite recursion when we send our own packets
        if (sendingPacket) return;

        // Block user movement input from being sent to server when W-tap is controlling movement
        if (isControllingMovement() && event.packet instanceof PlayerMoveC2SPacket packet) {
            // Get the current movement input from the user
            float userForward = (float) mc.player.input.getMovementInput().y;
            float userStrafe = (float) mc.player.input.getMovementInput().x;

            // If user is trying to move forward (W key), block it since W-tap is controlling movement
            if (userForward > 0) {
                event.cancel();
                sendingPacket = true;

                // Send a corrected packet without the user's forward input
                if (packet instanceof PlayerMoveC2SPacket.Full fullPacket) {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.Full(
                        fullPacket.getX(mc.player.getX()),
                        fullPacket.getY(mc.player.getY()),
                        fullPacket.getZ(mc.player.getZ()),
                        fullPacket.getYaw(mc.player.getYaw()),
                        fullPacket.getPitch(mc.player.getPitch()),
                        fullPacket.isOnGround(),
                        fullPacket.horizontalCollision()
                    ));
                } else if (packet instanceof PlayerMoveC2SPacket.LookAndOnGround lookPacket) {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.LookAndOnGround(
                        lookPacket.getYaw(mc.player.getYaw()),
                        lookPacket.getPitch(mc.player.getPitch()),
                        lookPacket.isOnGround(),
                        lookPacket.horizontalCollision()
                    ));
                } else if (packet instanceof PlayerMoveC2SPacket.PositionAndOnGround posPacket) {
                    mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(
                        posPacket.getX(mc.player.getX()),
                        posPacket.getY(mc.player.getY()),
                        posPacket.getZ(mc.player.getZ()),
                        posPacket.isOnGround(),
                        posPacket.horizontalCollision()
                    ));
                }

                sendingPacket = false;

                if (detailedLogging.get()) {
                    info("Blocked user W input from being sent to server (W-tap controlling movement)");
                }
            }
        }
    }

    // Check if module is currently performing a tap sequence
    public boolean isPerformingWTap() {
        return currentState != TapState.IDLE;
    }

    // Get current target for other modules
    public Entity getCurrentTarget() {
        return currentTarget;
    }

    // Get last measured distance
    public double getLastDistance() {
        return lastDistance;
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    // Mark that this module is taking action this tick
    private void markActionTaken() {
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            actionTakenField.setBoolean(null, true);
        } catch (Exception e) {
            // Ignore if we can't set the flag
        }
    }
}